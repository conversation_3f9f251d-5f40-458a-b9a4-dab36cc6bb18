{"************": {"discovery": null, "collect": [{"correlation.metrics": ["system.process.network.connection"], "system.process": [{"status": "Up", "system.process": "postgres|/usr/lib/postgresql/17/bin/postgres -D /var/lib/postgresql/17/main -c config_file=/etc/postgresql/17/main/postgresql.conf", "system.process.command": "/usr/lib/postgresql/17/bin/postgres -D /var/lib/postgresql/17/main -c config_file=/etc/postgresql/17/main/postgresql.conf", "system.process.cpu.percent": 0, "system.process.handles": 71, "system.process.id": 1133, "system.process.memory.used.bytes": 7897088, "system.process.memory.used.percent": 0, "system.process.name": "postgres", "system.process.started.time": " 35 days 22 hours 48 minutes 18 seconds", "system.process.started.time.seconds": 3106098, "system.process.threads": 1, "system.process.user": "postgres", "system.process.virtual.memory.bytes": 224317440}, {"status": "Up", "system.process": "postgres|postgres: 17/main: checkpointer", "system.process.command": "postgres: 17/main: checkpointer", "system.process.cpu.percent": 0, "system.process.handles": 69, "system.process.id": 1152, "system.process.memory.used.bytes": 12980224, "system.process.memory.used.percent": 0.1, "system.process.name": "postgres", "system.process.started.time": " 35 days 22 hours 48 minutes 15 seconds", "system.process.started.time.seconds": 3106095, "system.process.threads": 1, "system.process.user": "postgres", "system.process.virtual.memory.bytes": 224927744}, {"status": "Up", "system.process": "postgres|postgres: 17/main: background writer", "system.process.command": "postgres: 17/main: background writer", "system.process.cpu.percent": 0, "system.process.handles": 68, "system.process.id": 1153, "system.process.memory.used.bytes": 5623808, "system.process.memory.used.percent": 0, "system.process.name": "postgres", "system.process.started.time": " 35 days 22 hours 48 minutes 15 seconds", "system.process.started.time.seconds": 3106095, "system.process.threads": 1, "system.process.user": "postgres", "system.process.virtual.memory.bytes": 224456704}, {"status": "Up", "system.process": "postgres|postgres: 17/main: walwriter", "system.process.command": "postgres: 17/main: walwriter", "system.process.cpu.percent": 0, "system.process.handles": 69, "system.process.id": 1163, "system.process.memory.used.bytes": 6324224, "system.process.memory.used.percent": 0, "system.process.name": "postgres", "system.process.started.time": " 35 days 22 hours 48 minutes 14 seconds", "system.process.started.time.seconds": 3106094, "system.process.threads": 1, "system.process.user": "postgres", "system.process.virtual.memory.bytes": 224456704}, {"status": "Up", "system.process": "postgres|postgres: 17/main: autovacuum launcher", "system.process.command": "postgres: 17/main: autovacuum launcher", "system.process.cpu.percent": 0, "system.process.handles": 70, "system.process.id": 1164, "system.process.memory.used.bytes": 2654208, "system.process.memory.used.percent": 0, "system.process.name": "postgres", "system.process.started.time": " 35 days 22 hours 48 minutes 14 seconds", "system.process.started.time.seconds": 3106094, "system.process.threads": 1, "system.process.user": "postgres", "system.process.virtual.memory.bytes": 225939456}, {"status": "Up", "system.process": "postgres|postgres: 17/main: logical replication launcher", "system.process.command": "postgres: 17/main: logical replication launcher", "system.process.cpu.percent": 0, "system.process.handles": 70, "system.process.id": 1165, "system.process.memory.used.bytes": 1687552, "system.process.memory.used.percent": 0, "system.process.name": "postgres", "system.process.started.time": " 35 days 22 hours 48 minutes 14 seconds", "system.process.started.time.seconds": 3106094, "system.process.threads": 1, "system.process.user": "postgres", "system.process.virtual.memory.bytes": 225947648}, {"status": "Down", "system.process": "postgres|postgres: 17/main: motadata motadata 127.0.0.1(38208) idle", "system.process.command": "postgres: 17/main: motadata motadata 127.0.0.1(38208) idle", "system.process.name": "postgres"}, {"status": "Down", "system.process": "postgres|postgres: 17/main: motadata motadata 127.0.0.1(38218) idle", "system.process.command": "postgres: 17/main: motadata motadata 127.0.0.1(38218) idle", "system.process.name": "postgres"}, {"status": "Down", "system.process": "postgres|postgres: 17/main: motadata motadata 127.0.0.1(38228) idle", "system.process.command": "postgres: 17/main: motadata motadata 127.0.0.1(38228) idle", "system.process.name": "postgres"}, {"status": "Down", "system.process": "postgres|postgres: 17/main: motadata motadata 127.0.0.1(38242) idle", "system.process.command": "postgres: 17/main: motadata motadata 127.0.0.1(38242) idle", "system.process.name": "postgres"}, {"status": "Down", "system.process": "postgres|postgres: 17/main: motadata motadata 127.0.0.1(38254) idle", "system.process.command": "postgres: 17/main: motadata motadata 127.0.0.1(38254) idle", "system.process.name": "postgres"}], "system.process.network.connection": [{"system.process": "postgres|/usr/lib/postgresql/17/bin/postgres -D /var/lib/postgresql/17/main -c config_file=/etc/postgresql/17/main/postgresql.conf", "system.process.destination.ip": "************", "system.process.destination.port": "*", "system.process.source.ip": "************", "system.process.source.port": "5432"}]}, {"correlation.metrics": ["system.process.network.connection"], "system.process": [{"status": "Up", "system.process": "postgres|/usr/lib/postgresql/17/bin/postgres -D /var/lib/postgresql/17/main -c config_file=/etc/postgresql/17/main/postgresql.conf", "system.process.command": "/usr/lib/postgresql/17/bin/postgres -D /var/lib/postgresql/17/main -c config_file=/etc/postgresql/17/main/postgresql.conf", "system.process.cpu.percent": 0, "system.process.handles": 71, "system.process.id": 1133, "system.process.memory.used.bytes": 7897088, "system.process.memory.used.percent": 0, "system.process.name": "postgres", "system.process.started.time": " 35 days 22 hours 50 minutes 22 seconds", "system.process.started.time.seconds": 3106222, "system.process.threads": 1, "system.process.user": "postgres", "system.process.virtual.memory.bytes": 224317440}, {"status": "Up", "system.process": "postgres|postgres: 17/main: checkpointer", "system.process.command": "postgres: 17/main: checkpointer", "system.process.cpu.percent": 0, "system.process.handles": 69, "system.process.id": 1152, "system.process.memory.used.bytes": 12980224, "system.process.memory.used.percent": 0.1, "system.process.name": "postgres", "system.process.started.time": " 35 days 22 hours 50 minutes 19 seconds", "system.process.started.time.seconds": 3106219, "system.process.threads": 1, "system.process.user": "postgres", "system.process.virtual.memory.bytes": 224927744}, {"status": "Up", "system.process": "postgres|postgres: 17/main: background writer", "system.process.command": "postgres: 17/main: background writer", "system.process.cpu.percent": 0, "system.process.handles": 68, "system.process.id": 1153, "system.process.memory.used.bytes": 5623808, "system.process.memory.used.percent": 0, "system.process.name": "postgres", "system.process.started.time": " 35 days 22 hours 50 minutes 19 seconds", "system.process.started.time.seconds": 3106219, "system.process.threads": 1, "system.process.user": "postgres", "system.process.virtual.memory.bytes": 224456704}, {"status": "Up", "system.process": "postgres|postgres: 17/main: walwriter", "system.process.command": "postgres: 17/main: walwriter", "system.process.cpu.percent": 0, "system.process.handles": 69, "system.process.id": 1163, "system.process.memory.used.bytes": 6324224, "system.process.memory.used.percent": 0, "system.process.name": "postgres", "system.process.started.time": " 35 days 22 hours 50 minutes 18 seconds", "system.process.started.time.seconds": 3106218, "system.process.threads": 1, "system.process.user": "postgres", "system.process.virtual.memory.bytes": 224456704}, {"status": "Up", "system.process": "postgres|postgres: 17/main: autovacuum launcher", "system.process.command": "postgres: 17/main: autovacuum launcher", "system.process.cpu.percent": 0, "system.process.handles": 70, "system.process.id": 1164, "system.process.memory.used.bytes": 2654208, "system.process.memory.used.percent": 0, "system.process.name": "postgres", "system.process.started.time": " 35 days 22 hours 50 minutes 18 seconds", "system.process.started.time.seconds": 3106218, "system.process.threads": 1, "system.process.user": "postgres", "system.process.virtual.memory.bytes": 225939456}, {"status": "Up", "system.process": "postgres|postgres: 17/main: logical replication launcher", "system.process.command": "postgres: 17/main: logical replication launcher", "system.process.cpu.percent": 0, "system.process.handles": 70, "system.process.id": 1165, "system.process.memory.used.bytes": 1687552, "system.process.memory.used.percent": 0, "system.process.name": "postgres", "system.process.started.time": " 35 days 22 hours 50 minutes 18 seconds", "system.process.started.time.seconds": 3106218, "system.process.threads": 1, "system.process.user": "postgres", "system.process.virtual.memory.bytes": 225947648}, {"status": "Down", "system.process": "postgres|postgres: 17/main: motadata motadata 127.0.0.1(38208) idle", "system.process.command": "postgres: 17/main: motadata motadata 127.0.0.1(38208) idle", "system.process.name": "postgres"}, {"status": "Down", "system.process": "postgres|postgres: 17/main: motadata motadata 127.0.0.1(38218) idle", "system.process.command": "postgres: 17/main: motadata motadata 127.0.0.1(38218) idle", "system.process.name": "postgres"}, {"status": "Down", "system.process": "postgres|postgres: 17/main: motadata motadata 127.0.0.1(38228) idle", "system.process.command": "postgres: 17/main: motadata motadata 127.0.0.1(38228) idle", "system.process.name": "postgres"}, {"status": "Down", "system.process": "postgres|postgres: 17/main: motadata motadata 127.0.0.1(38242) idle", "system.process.command": "postgres: 17/main: motadata motadata 127.0.0.1(38242) idle", "system.process.name": "postgres"}, {"status": "Down", "system.process": "postgres|postgres: 17/main: motadata motadata 127.0.0.1(38254) idle", "system.process.command": "postgres: 17/main: motadata motadata 127.0.0.1(38254) idle", "system.process.name": "postgres"}], "system.process.network.connection": [{"system.process": "postgres|/usr/lib/postgresql/17/bin/postgres -D /var/lib/postgresql/17/main -c config_file=/etc/postgresql/17/main/postgresql.conf", "system.process.destination.ip": "************", "system.process.destination.port": "*", "system.process.source.ip": "************", "system.process.source.port": "5432"}]}, {"correlation.metrics": ["system.process.network.connection"], "system.process": [{"status": "Up", "system.process": "postgres|postgres: 17/main: autovacuum launcher", "system.process.command": "postgres: 17/main: autovacuum launcher", "system.process.cpu.percent": 0, "system.process.handles": 70, "system.process.id": 1164, "system.process.memory.used.bytes": 2654208, "system.process.memory.used.percent": 0, "system.process.name": "postgres", "system.process.started.time": " 35 days 22 hours 55 minutes 17 seconds", "system.process.started.time.seconds": 3106517, "system.process.threads": 1, "system.process.user": "postgres", "system.process.virtual.memory.bytes": 225939456}, {"status": "Up", "system.process": "postgres|postgres: 17/main: logical replication launcher", "system.process.command": "postgres: 17/main: logical replication launcher", "system.process.cpu.percent": 0, "system.process.handles": 70, "system.process.id": 1165, "system.process.memory.used.bytes": 1687552, "system.process.memory.used.percent": 0, "system.process.name": "postgres", "system.process.started.time": " 35 days 22 hours 55 minutes 17 seconds", "system.process.started.time.seconds": 3106517, "system.process.threads": 1, "system.process.user": "postgres", "system.process.virtual.memory.bytes": 225947648}, {"status": "Up", "system.process": "postgres|/usr/lib/postgresql/17/bin/postgres -D /var/lib/postgresql/17/main -c config_file=/etc/postgresql/17/main/postgresql.conf", "system.process.command": "/usr/lib/postgresql/17/bin/postgres -D /var/lib/postgresql/17/main -c config_file=/etc/postgresql/17/main/postgresql.conf", "system.process.cpu.percent": 0, "system.process.handles": 71, "system.process.id": 1133, "system.process.memory.used.bytes": 7897088, "system.process.memory.used.percent": 0, "system.process.name": "postgres", "system.process.started.time": " 35 days 22 hours 55 minutes 21 seconds", "system.process.started.time.seconds": 3106521, "system.process.threads": 1, "system.process.user": "postgres", "system.process.virtual.memory.bytes": 224317440}, {"status": "Up", "system.process": "postgres|postgres: 17/main: checkpointer", "system.process.command": "postgres: 17/main: checkpointer", "system.process.cpu.percent": 0, "system.process.handles": 69, "system.process.id": 1152, "system.process.memory.used.bytes": 12980224, "system.process.memory.used.percent": 0.1, "system.process.name": "postgres", "system.process.started.time": " 35 days 22 hours 55 minutes 18 seconds", "system.process.started.time.seconds": 3106518, "system.process.threads": 1, "system.process.user": "postgres", "system.process.virtual.memory.bytes": 224927744}, {"status": "Up", "system.process": "postgres|postgres: 17/main: background writer", "system.process.command": "postgres: 17/main: background writer", "system.process.cpu.percent": 0, "system.process.handles": 68, "system.process.id": 1153, "system.process.memory.used.bytes": 5623808, "system.process.memory.used.percent": 0, "system.process.name": "postgres", "system.process.started.time": " 35 days 22 hours 55 minutes 18 seconds", "system.process.started.time.seconds": 3106518, "system.process.threads": 1, "system.process.user": "postgres", "system.process.virtual.memory.bytes": 224456704}, {"status": "Up", "system.process": "postgres|postgres: 17/main: walwriter", "system.process.command": "postgres: 17/main: walwriter", "system.process.cpu.percent": 0, "system.process.handles": 69, "system.process.id": 1163, "system.process.memory.used.bytes": 6324224, "system.process.memory.used.percent": 0, "system.process.name": "postgres", "system.process.started.time": " 35 days 22 hours 55 minutes 17 seconds", "system.process.started.time.seconds": 3106517, "system.process.threads": 1, "system.process.user": "postgres", "system.process.virtual.memory.bytes": 224456704}, {"status": "Down", "system.process": "postgres|postgres: 17/main: motadata motadata 127.0.0.1(38208) idle", "system.process.command": "postgres: 17/main: motadata motadata 127.0.0.1(38208) idle", "system.process.name": "postgres"}, {"status": "Down", "system.process": "postgres|postgres: 17/main: motadata motadata 127.0.0.1(38218) idle", "system.process.command": "postgres: 17/main: motadata motadata 127.0.0.1(38218) idle", "system.process.name": "postgres"}, {"status": "Down", "system.process": "postgres|postgres: 17/main: motadata motadata 127.0.0.1(38228) idle", "system.process.command": "postgres: 17/main: motadata motadata 127.0.0.1(38228) idle", "system.process.name": "postgres"}, {"status": "Down", "system.process": "postgres|postgres: 17/main: motadata motadata 127.0.0.1(38242) idle", "system.process.command": "postgres: 17/main: motadata motadata 127.0.0.1(38242) idle", "system.process.name": "postgres"}, {"status": "Down", "system.process": "postgres|postgres: 17/main: motadata motadata 127.0.0.1(38254) idle", "system.process.command": "postgres: 17/main: motadata motadata 127.0.0.1(38254) idle", "system.process.name": "postgres"}], "system.process.network.connection": [{"system.process": "postgres|/usr/lib/postgresql/17/bin/postgres -D /var/lib/postgresql/17/main -c config_file=/etc/postgresql/17/main/postgresql.conf", "system.process.destination.ip": "************", "system.process.destination.port": "*", "system.process.source.ip": "************", "system.process.source.port": "5432"}]}, {"correlation.metrics": ["system.process.network.connection"], "system.process": [{"status": "Up", "system.process": "postgres|/usr/lib/postgresql/17/bin/postgres -D /var/lib/postgresql/17/main -c config_file=/etc/postgresql/17/main/postgresql.conf", "system.process.command": "/usr/lib/postgresql/17/bin/postgres -D /var/lib/postgresql/17/main -c config_file=/etc/postgresql/17/main/postgresql.conf", "system.process.cpu.percent": 0, "system.process.handles": 71, "system.process.id": 1133, "system.process.memory.used.bytes": 7897088, "system.process.memory.used.percent": 0, "system.process.name": "postgres", "system.process.started.time": " 35 days 23 hours 0 minute 21 seconds", "system.process.started.time.seconds": 3106821, "system.process.threads": 1, "system.process.user": "postgres", "system.process.virtual.memory.bytes": 224317440}, {"status": "Up", "system.process": "postgres|postgres: 17/main: checkpointer", "system.process.command": "postgres: 17/main: checkpointer", "system.process.cpu.percent": 0, "system.process.handles": 69, "system.process.id": 1152, "system.process.memory.used.bytes": 12980224, "system.process.memory.used.percent": 0.1, "system.process.name": "postgres", "system.process.started.time": " 35 days 23 hours 0 minute 18 seconds", "system.process.started.time.seconds": 3106818, "system.process.threads": 1, "system.process.user": "postgres", "system.process.virtual.memory.bytes": 224927744}, {"status": "Up", "system.process": "postgres|postgres: 17/main: background writer", "system.process.command": "postgres: 17/main: background writer", "system.process.cpu.percent": 0, "system.process.handles": 68, "system.process.id": 1153, "system.process.memory.used.bytes": 5623808, "system.process.memory.used.percent": 0, "system.process.name": "postgres", "system.process.started.time": " 35 days 23 hours 0 minute 18 seconds", "system.process.started.time.seconds": 3106818, "system.process.threads": 1, "system.process.user": "postgres", "system.process.virtual.memory.bytes": 224456704}, {"status": "Up", "system.process": "postgres|postgres: 17/main: walwriter", "system.process.command": "postgres: 17/main: walwriter", "system.process.cpu.percent": 0, "system.process.handles": 69, "system.process.id": 1163, "system.process.memory.used.bytes": 6324224, "system.process.memory.used.percent": 0, "system.process.name": "postgres", "system.process.started.time": " 35 days 23 hours 0 minute 17 seconds", "system.process.started.time.seconds": 3106817, "system.process.threads": 1, "system.process.user": "postgres", "system.process.virtual.memory.bytes": 224456704}, {"status": "Up", "system.process": "postgres|postgres: 17/main: autovacuum launcher", "system.process.command": "postgres: 17/main: autovacuum launcher", "system.process.cpu.percent": 0, "system.process.handles": 70, "system.process.id": 1164, "system.process.memory.used.bytes": 2654208, "system.process.memory.used.percent": 0, "system.process.name": "postgres", "system.process.started.time": " 35 days 23 hours 0 minute 17 seconds", "system.process.started.time.seconds": 3106817, "system.process.threads": 1, "system.process.user": "postgres", "system.process.virtual.memory.bytes": 225939456}, {"status": "Up", "system.process": "postgres|postgres: 17/main: logical replication launcher", "system.process.command": "postgres: 17/main: logical replication launcher", "system.process.cpu.percent": 0, "system.process.handles": 70, "system.process.id": 1165, "system.process.memory.used.bytes": 1687552, "system.process.memory.used.percent": 0, "system.process.name": "postgres", "system.process.started.time": " 35 days 23 hours 0 minute 17 seconds", "system.process.started.time.seconds": 3106817, "system.process.threads": 1, "system.process.user": "postgres", "system.process.virtual.memory.bytes": 225947648}, {"status": "Down", "system.process": "postgres|postgres: 17/main: motadata motadata 127.0.0.1(38208) idle", "system.process.command": "postgres: 17/main: motadata motadata 127.0.0.1(38208) idle", "system.process.name": "postgres"}, {"status": "Down", "system.process": "postgres|postgres: 17/main: motadata motadata 127.0.0.1(38218) idle", "system.process.command": "postgres: 17/main: motadata motadata 127.0.0.1(38218) idle", "system.process.name": "postgres"}, {"status": "Down", "system.process": "postgres|postgres: 17/main: motadata motadata 127.0.0.1(38228) idle", "system.process.command": "postgres: 17/main: motadata motadata 127.0.0.1(38228) idle", "system.process.name": "postgres"}, {"status": "Down", "system.process": "postgres|postgres: 17/main: motadata motadata 127.0.0.1(38242) idle", "system.process.command": "postgres: 17/main: motadata motadata 127.0.0.1(38242) idle", "system.process.name": "postgres"}, {"status": "Down", "system.process": "postgres|postgres: 17/main: motadata motadata 127.0.0.1(38254) idle", "system.process.command": "postgres: 17/main: motadata motadata 127.0.0.1(38254) idle", "system.process.name": "postgres"}], "system.process.network.connection": [{"system.process": "postgres|/usr/lib/postgresql/17/bin/postgres -D /var/lib/postgresql/17/main -c config_file=/etc/postgresql/17/main/postgresql.conf", "system.process.destination.ip": "************", "system.process.destination.port": "*", "system.process.source.ip": "************", "system.process.source.port": "5432"}]}, {"correlation.metrics": ["system.process.network.connection"], "system.process": [{"status": "Up", "system.process": "postgres|postgres: 17/main: walwriter", "system.process.command": "postgres: 17/main: walwriter", "system.process.cpu.percent": 0, "system.process.handles": 69, "system.process.id": 1163, "system.process.memory.used.bytes": 6324224, "system.process.memory.used.percent": 0, "system.process.name": "postgres", "system.process.started.time": " 35 days 23 hours 5 minutes 17 seconds", "system.process.started.time.seconds": 3107117, "system.process.threads": 1, "system.process.user": "postgres", "system.process.virtual.memory.bytes": 224456704}, {"status": "Up", "system.process": "postgres|postgres: 17/main: autovacuum launcher", "system.process.command": "postgres: 17/main: autovacuum launcher", "system.process.cpu.percent": 0, "system.process.handles": 70, "system.process.id": 1164, "system.process.memory.used.bytes": 2654208, "system.process.memory.used.percent": 0, "system.process.name": "postgres", "system.process.started.time": " 35 days 23 hours 5 minutes 17 seconds", "system.process.started.time.seconds": 3107117, "system.process.threads": 1, "system.process.user": "postgres", "system.process.virtual.memory.bytes": 225939456}, {"status": "Up", "system.process": "postgres|postgres: 17/main: logical replication launcher", "system.process.command": "postgres: 17/main: logical replication launcher", "system.process.cpu.percent": 0, "system.process.handles": 70, "system.process.id": 1165, "system.process.memory.used.bytes": 1687552, "system.process.memory.used.percent": 0, "system.process.name": "postgres", "system.process.started.time": " 35 days 23 hours 5 minutes 17 seconds", "system.process.started.time.seconds": 3107117, "system.process.threads": 1, "system.process.user": "postgres", "system.process.virtual.memory.bytes": 225947648}, {"status": "Up", "system.process": "postgres|/usr/lib/postgresql/17/bin/postgres -D /var/lib/postgresql/17/main -c config_file=/etc/postgresql/17/main/postgresql.conf", "system.process.command": "/usr/lib/postgresql/17/bin/postgres -D /var/lib/postgresql/17/main -c config_file=/etc/postgresql/17/main/postgresql.conf", "system.process.cpu.percent": 0, "system.process.handles": 71, "system.process.id": 1133, "system.process.memory.used.bytes": 7897088, "system.process.memory.used.percent": 0, "system.process.name": "postgres", "system.process.started.time": " 35 days 23 hours 5 minutes 21 seconds", "system.process.started.time.seconds": 3107121, "system.process.threads": 1, "system.process.user": "postgres", "system.process.virtual.memory.bytes": 224317440}, {"status": "Up", "system.process": "postgres|postgres: 17/main: checkpointer", "system.process.command": "postgres: 17/main: checkpointer", "system.process.cpu.percent": 0, "system.process.handles": 69, "system.process.id": 1152, "system.process.memory.used.bytes": 12980224, "system.process.memory.used.percent": 0.1, "system.process.name": "postgres", "system.process.started.time": " 35 days 23 hours 5 minutes 18 seconds", "system.process.started.time.seconds": 3107118, "system.process.threads": 1, "system.process.user": "postgres", "system.process.virtual.memory.bytes": 224927744}, {"status": "Up", "system.process": "postgres|postgres: 17/main: background writer", "system.process.command": "postgres: 17/main: background writer", "system.process.cpu.percent": 0, "system.process.handles": 68, "system.process.id": 1153, "system.process.memory.used.bytes": 5623808, "system.process.memory.used.percent": 0, "system.process.name": "postgres", "system.process.started.time": " 35 days 23 hours 5 minutes 18 seconds", "system.process.started.time.seconds": 3107118, "system.process.threads": 1, "system.process.user": "postgres", "system.process.virtual.memory.bytes": 224456704}, {"status": "Down", "system.process": "postgres|postgres: 17/main: motadata motadata 127.0.0.1(38208) idle", "system.process.command": "postgres: 17/main: motadata motadata 127.0.0.1(38208) idle", "system.process.name": "postgres"}, {"status": "Down", "system.process": "postgres|postgres: 17/main: motadata motadata 127.0.0.1(38218) idle", "system.process.command": "postgres: 17/main: motadata motadata 127.0.0.1(38218) idle", "system.process.name": "postgres"}, {"status": "Down", "system.process": "postgres|postgres: 17/main: motadata motadata 127.0.0.1(38228) idle", "system.process.command": "postgres: 17/main: motadata motadata 127.0.0.1(38228) idle", "system.process.name": "postgres"}, {"status": "Down", "system.process": "postgres|postgres: 17/main: motadata motadata 127.0.0.1(38242) idle", "system.process.command": "postgres: 17/main: motadata motadata 127.0.0.1(38242) idle", "system.process.name": "postgres"}, {"status": "Down", "system.process": "postgres|postgres: 17/main: motadata motadata 127.0.0.1(38254) idle", "system.process.command": "postgres: 17/main: motadata motadata 127.0.0.1(38254) idle", "system.process.name": "postgres"}], "system.process.network.connection": [{"system.process": "postgres|/usr/lib/postgresql/17/bin/postgres -D /var/lib/postgresql/17/main -c config_file=/etc/postgresql/17/main/postgresql.conf", "system.process.destination.ip": "************", "system.process.destination.port": "*", "system.process.source.ip": "************", "system.process.source.port": "5432"}]}, {"correlation.metrics": ["system.process.network.connection"], "system.process": [{"status": "Up", "system.process": "postgres|postgres: 17/main: logical replication launcher", "system.process.command": "postgres: 17/main: logical replication launcher", "system.process.cpu.percent": 0, "system.process.handles": 70, "system.process.id": 1165, "system.process.memory.used.bytes": 1687552, "system.process.memory.used.percent": 0, "system.process.name": "postgres", "system.process.started.time": " 35 days 23 hours 10 minutes 18 seconds", "system.process.started.time.seconds": 3107418, "system.process.threads": 1, "system.process.user": "postgres", "system.process.virtual.memory.bytes": 225947648}, {"status": "Up", "system.process": "postgres|/usr/lib/postgresql/17/bin/postgres -D /var/lib/postgresql/17/main -c config_file=/etc/postgresql/17/main/postgresql.conf", "system.process.command": "/usr/lib/postgresql/17/bin/postgres -D /var/lib/postgresql/17/main -c config_file=/etc/postgresql/17/main/postgresql.conf", "system.process.cpu.percent": 0, "system.process.handles": 71, "system.process.id": 1133, "system.process.memory.used.bytes": 7897088, "system.process.memory.used.percent": 0, "system.process.name": "postgres", "system.process.started.time": " 35 days 23 hours 10 minutes 22 seconds", "system.process.started.time.seconds": 3107422, "system.process.threads": 1, "system.process.user": "postgres", "system.process.virtual.memory.bytes": 224317440}, {"status": "Up", "system.process": "postgres|postgres: 17/main: checkpointer", "system.process.command": "postgres: 17/main: checkpointer", "system.process.cpu.percent": 0, "system.process.handles": 69, "system.process.id": 1152, "system.process.memory.used.bytes": 12980224, "system.process.memory.used.percent": 0.1, "system.process.name": "postgres", "system.process.started.time": " 35 days 23 hours 10 minutes 19 seconds", "system.process.started.time.seconds": 3107419, "system.process.threads": 1, "system.process.user": "postgres", "system.process.virtual.memory.bytes": 224927744}, {"status": "Up", "system.process": "postgres|postgres: 17/main: background writer", "system.process.command": "postgres: 17/main: background writer", "system.process.cpu.percent": 0, "system.process.handles": 68, "system.process.id": 1153, "system.process.memory.used.bytes": 5623808, "system.process.memory.used.percent": 0, "system.process.name": "postgres", "system.process.started.time": " 35 days 23 hours 10 minutes 19 seconds", "system.process.started.time.seconds": 3107419, "system.process.threads": 1, "system.process.user": "postgres", "system.process.virtual.memory.bytes": 224456704}, {"status": "Up", "system.process": "postgres|postgres: 17/main: walwriter", "system.process.command": "postgres: 17/main: walwriter", "system.process.cpu.percent": 0, "system.process.handles": 69, "system.process.id": 1163, "system.process.memory.used.bytes": 6324224, "system.process.memory.used.percent": 0, "system.process.name": "postgres", "system.process.started.time": " 35 days 23 hours 10 minutes 18 seconds", "system.process.started.time.seconds": 3107418, "system.process.threads": 1, "system.process.user": "postgres", "system.process.virtual.memory.bytes": 224456704}, {"status": "Up", "system.process": "postgres|postgres: 17/main: autovacuum launcher", "system.process.command": "postgres: 17/main: autovacuum launcher", "system.process.cpu.percent": 0, "system.process.handles": 70, "system.process.id": 1164, "system.process.memory.used.bytes": 2654208, "system.process.memory.used.percent": 0, "system.process.name": "postgres", "system.process.started.time": " 35 days 23 hours 10 minutes 18 seconds", "system.process.started.time.seconds": 3107418, "system.process.threads": 1, "system.process.user": "postgres", "system.process.virtual.memory.bytes": 225939456}, {"status": "Down", "system.process": "postgres|postgres: 17/main: motadata motadata 127.0.0.1(38208) idle", "system.process.command": "postgres: 17/main: motadata motadata 127.0.0.1(38208) idle", "system.process.name": "postgres"}, {"status": "Down", "system.process": "postgres|postgres: 17/main: motadata motadata 127.0.0.1(38218) idle", "system.process.command": "postgres: 17/main: motadata motadata 127.0.0.1(38218) idle", "system.process.name": "postgres"}, {"status": "Down", "system.process": "postgres|postgres: 17/main: motadata motadata 127.0.0.1(38228) idle", "system.process.command": "postgres: 17/main: motadata motadata 127.0.0.1(38228) idle", "system.process.name": "postgres"}, {"status": "Down", "system.process": "postgres|postgres: 17/main: motadata motadata 127.0.0.1(38242) idle", "system.process.command": "postgres: 17/main: motadata motadata 127.0.0.1(38242) idle", "system.process.name": "postgres"}, {"status": "Down", "system.process": "postgres|postgres: 17/main: motadata motadata 127.0.0.1(38254) idle", "system.process.command": "postgres: 17/main: motadata motadata 127.0.0.1(38254) idle", "system.process.name": "postgres"}], "system.process.network.connection": [{"system.process": "postgres|/usr/lib/postgresql/17/bin/postgres -D /var/lib/postgresql/17/main -c config_file=/etc/postgresql/17/main/postgresql.conf", "system.process.destination.ip": "************", "system.process.destination.port": "*", "system.process.source.ip": "************", "system.process.source.port": "5432"}]}, {"correlation.metrics": ["system.process.network.connection"], "system.process": [{"status": "Up", "system.process": "postgres|postgres: 17/main: background writer", "system.process.command": "postgres: 17/main: background writer", "system.process.cpu.percent": 0, "system.process.handles": 68, "system.process.id": 1153, "system.process.memory.used.bytes": 5623808, "system.process.memory.used.percent": 0, "system.process.name": "postgres", "system.process.started.time": " 35 days 23 hours 15 minutes 19 seconds", "system.process.started.time.seconds": 3107719, "system.process.threads": 1, "system.process.user": "postgres", "system.process.virtual.memory.bytes": 224456704}, {"status": "Up", "system.process": "postgres|postgres: 17/main: walwriter", "system.process.command": "postgres: 17/main: walwriter", "system.process.cpu.percent": 0, "system.process.handles": 69, "system.process.id": 1163, "system.process.memory.used.bytes": 6324224, "system.process.memory.used.percent": 0, "system.process.name": "postgres", "system.process.started.time": " 35 days 23 hours 15 minutes 18 seconds", "system.process.started.time.seconds": 3107718, "system.process.threads": 1, "system.process.user": "postgres", "system.process.virtual.memory.bytes": 224456704}, {"status": "Up", "system.process": "postgres|postgres: 17/main: autovacuum launcher", "system.process.command": "postgres: 17/main: autovacuum launcher", "system.process.cpu.percent": 0, "system.process.handles": 70, "system.process.id": 1164, "system.process.memory.used.bytes": 2654208, "system.process.memory.used.percent": 0, "system.process.name": "postgres", "system.process.started.time": " 35 days 23 hours 15 minutes 18 seconds", "system.process.started.time.seconds": 3107718, "system.process.threads": 1, "system.process.user": "postgres", "system.process.virtual.memory.bytes": 225939456}, {"status": "Up", "system.process": "postgres|postgres: 17/main: logical replication launcher", "system.process.command": "postgres: 17/main: logical replication launcher", "system.process.cpu.percent": 0, "system.process.handles": 70, "system.process.id": 1165, "system.process.memory.used.bytes": 1687552, "system.process.memory.used.percent": 0, "system.process.name": "postgres", "system.process.started.time": " 35 days 23 hours 15 minutes 18 seconds", "system.process.started.time.seconds": 3107718, "system.process.threads": 1, "system.process.user": "postgres", "system.process.virtual.memory.bytes": 225947648}, {"status": "Up", "system.process": "postgres|/usr/lib/postgresql/17/bin/postgres -D /var/lib/postgresql/17/main -c config_file=/etc/postgresql/17/main/postgresql.conf", "system.process.command": "/usr/lib/postgresql/17/bin/postgres -D /var/lib/postgresql/17/main -c config_file=/etc/postgresql/17/main/postgresql.conf", "system.process.cpu.percent": 0, "system.process.handles": 71, "system.process.id": 1133, "system.process.memory.used.bytes": 7897088, "system.process.memory.used.percent": 0, "system.process.name": "postgres", "system.process.started.time": " 35 days 23 hours 15 minutes 22 seconds", "system.process.started.time.seconds": 3107722, "system.process.threads": 1, "system.process.user": "postgres", "system.process.virtual.memory.bytes": 224317440}, {"status": "Up", "system.process": "postgres|postgres: 17/main: checkpointer", "system.process.command": "postgres: 17/main: checkpointer", "system.process.cpu.percent": 0, "system.process.handles": 69, "system.process.id": 1152, "system.process.memory.used.bytes": 12980224, "system.process.memory.used.percent": 0.1, "system.process.name": "postgres", "system.process.started.time": " 35 days 23 hours 15 minutes 19 seconds", "system.process.started.time.seconds": 3107719, "system.process.threads": 1, "system.process.user": "postgres", "system.process.virtual.memory.bytes": 224927744}, {"status": "Down", "system.process": "postgres|postgres: 17/main: motadata motadata 127.0.0.1(38208) idle", "system.process.command": "postgres: 17/main: motadata motadata 127.0.0.1(38208) idle", "system.process.name": "postgres"}, {"status": "Down", "system.process": "postgres|postgres: 17/main: motadata motadata 127.0.0.1(38218) idle", "system.process.command": "postgres: 17/main: motadata motadata 127.0.0.1(38218) idle", "system.process.name": "postgres"}, {"status": "Down", "system.process": "postgres|postgres: 17/main: motadata motadata 127.0.0.1(38228) idle", "system.process.command": "postgres: 17/main: motadata motadata 127.0.0.1(38228) idle", "system.process.name": "postgres"}, {"status": "Down", "system.process": "postgres|postgres: 17/main: motadata motadata 127.0.0.1(38242) idle", "system.process.command": "postgres: 17/main: motadata motadata 127.0.0.1(38242) idle", "system.process.name": "postgres"}, {"status": "Down", "system.process": "postgres|postgres: 17/main: motadata motadata 127.0.0.1(38254) idle", "system.process.command": "postgres: 17/main: motadata motadata 127.0.0.1(38254) idle", "system.process.name": "postgres"}], "system.process.network.connection": [{"system.process": "postgres|/usr/lib/postgresql/17/bin/postgres -D /var/lib/postgresql/17/main -c config_file=/etc/postgresql/17/main/postgresql.conf", "system.process.destination.ip": "************", "system.process.destination.port": "*", "system.process.source.ip": "************", "system.process.source.port": "5432"}]}, {"correlation.metrics": ["system.process.network.connection"], "system.process": [{"status": "Up", "system.process": "postgres|postgres: 17/main: autovacuum launcher", "system.process.command": "postgres: 17/main: autovacuum launcher", "system.process.cpu.percent": 0, "system.process.handles": 70, "system.process.id": 1164, "system.process.memory.used.bytes": 2654208, "system.process.memory.used.percent": 0, "system.process.name": "postgres", "system.process.started.time": " 35 days 23 hours 25 minutes 18 seconds", "system.process.started.time.seconds": 3108318, "system.process.threads": 1, "system.process.user": "postgres", "system.process.virtual.memory.bytes": 225939456}, {"status": "Up", "system.process": "postgres|postgres: 17/main: logical replication launcher", "system.process.command": "postgres: 17/main: logical replication launcher", "system.process.cpu.percent": 0, "system.process.handles": 70, "system.process.id": 1165, "system.process.memory.used.bytes": 1687552, "system.process.memory.used.percent": 0, "system.process.name": "postgres", "system.process.started.time": " 35 days 23 hours 25 minutes 18 seconds", "system.process.started.time.seconds": 3108318, "system.process.threads": 1, "system.process.user": "postgres", "system.process.virtual.memory.bytes": 225947648}, {"status": "Up", "system.process": "postgres|/usr/lib/postgresql/17/bin/postgres -D /var/lib/postgresql/17/main -c config_file=/etc/postgresql/17/main/postgresql.conf", "system.process.command": "/usr/lib/postgresql/17/bin/postgres -D /var/lib/postgresql/17/main -c config_file=/etc/postgresql/17/main/postgresql.conf", "system.process.cpu.percent": 0, "system.process.handles": 71, "system.process.id": 1133, "system.process.memory.used.bytes": 7897088, "system.process.memory.used.percent": 0, "system.process.name": "postgres", "system.process.started.time": " 35 days 23 hours 25 minutes 22 seconds", "system.process.started.time.seconds": 3108322, "system.process.threads": 1, "system.process.user": "postgres", "system.process.virtual.memory.bytes": 224317440}, {"status": "Up", "system.process": "postgres|postgres: 17/main: checkpointer", "system.process.command": "postgres: 17/main: checkpointer", "system.process.cpu.percent": 0, "system.process.handles": 69, "system.process.id": 1152, "system.process.memory.used.bytes": 12980224, "system.process.memory.used.percent": 0.1, "system.process.name": "postgres", "system.process.started.time": " 35 days 23 hours 25 minutes 19 seconds", "system.process.started.time.seconds": 3108319, "system.process.threads": 1, "system.process.user": "postgres", "system.process.virtual.memory.bytes": 224927744}, {"status": "Up", "system.process": "postgres|postgres: 17/main: background writer", "system.process.command": "postgres: 17/main: background writer", "system.process.cpu.percent": 0, "system.process.handles": 68, "system.process.id": 1153, "system.process.memory.used.bytes": 5623808, "system.process.memory.used.percent": 0, "system.process.name": "postgres", "system.process.started.time": " 35 days 23 hours 25 minutes 19 seconds", "system.process.started.time.seconds": 3108319, "system.process.threads": 1, "system.process.user": "postgres", "system.process.virtual.memory.bytes": 224456704}, {"status": "Up", "system.process": "postgres|postgres: 17/main: walwriter", "system.process.command": "postgres: 17/main: walwriter", "system.process.cpu.percent": 0, "system.process.handles": 69, "system.process.id": 1163, "system.process.memory.used.bytes": 6324224, "system.process.memory.used.percent": 0, "system.process.name": "postgres", "system.process.started.time": " 35 days 23 hours 25 minutes 18 seconds", "system.process.started.time.seconds": 3108318, "system.process.threads": 1, "system.process.user": "postgres", "system.process.virtual.memory.bytes": 224456704}, {"status": "Down", "system.process": "postgres|postgres: 17/main: motadata motadata 127.0.0.1(38208) idle", "system.process.command": "postgres: 17/main: motadata motadata 127.0.0.1(38208) idle", "system.process.name": "postgres"}, {"status": "Down", "system.process": "postgres|postgres: 17/main: motadata motadata 127.0.0.1(38218) idle", "system.process.command": "postgres: 17/main: motadata motadata 127.0.0.1(38218) idle", "system.process.name": "postgres"}, {"status": "Down", "system.process": "postgres|postgres: 17/main: motadata motadata 127.0.0.1(38228) idle", "system.process.command": "postgres: 17/main: motadata motadata 127.0.0.1(38228) idle", "system.process.name": "postgres"}, {"status": "Down", "system.process": "postgres|postgres: 17/main: motadata motadata 127.0.0.1(38242) idle", "system.process.command": "postgres: 17/main: motadata motadata 127.0.0.1(38242) idle", "system.process.name": "postgres"}, {"status": "Down", "system.process": "postgres|postgres: 17/main: motadata motadata 127.0.0.1(38254) idle", "system.process.command": "postgres: 17/main: motadata motadata 127.0.0.1(38254) idle", "system.process.name": "postgres"}], "system.process.network.connection": [{"system.process": "postgres|/usr/lib/postgresql/17/bin/postgres -D /var/lib/postgresql/17/main -c config_file=/etc/postgresql/17/main/postgresql.conf", "system.process.destination.ip": "************", "system.process.destination.port": "*", "system.process.source.ip": "************", "system.process.source.port": "5432"}]}], "rediscovery": null}, "************": {"discovery": null, "collect": [{"correlation.metrics": ["system.process.network.connection"], "system.process": [{"status": "Up", "system.process": "postgres|postgres: 10/main: autovacuum launcher process", "system.process.command": "postgres: 10/main: autovacuum launcher process", "system.process.cpu.percent": 0, "system.process.handles": 74, "system.process.id": 1573, "system.process.io.bytes.per.sec": -1025, "system.process.memory.used.bytes": 6205440, "system.process.memory.used.percent": 0.1, "system.process.name": "postgres", "system.process.started.time": " 141 days 22 hours 28 minutes 54 seconds", "system.process.started.time.seconds": 12263334, "system.process.threads": 1, "system.process.user": "postgres", "system.process.virtual.memory.bytes": 326885376}, {"status": "Up", "system.process": "postgres|postgres: 10/main: stats collector process", "system.process.command": "postgres: 10/main: stats collector process", "system.process.cpu.percent": 0, "system.process.handles": 70, "system.process.id": 1576, "system.process.memory.used.bytes": 3063808, "system.process.memory.used.percent": 0, "system.process.name": "postgres", "system.process.started.time": " 141 days 22 hours 28 minutes 54 seconds", "system.process.started.time.seconds": 12263334, "system.process.threads": 1, "system.process.user": "postgres", "system.process.virtual.memory.bytes": 177905664}, {"status": "Up", "system.process": "postgres|postgres: 10/main: bgworker: logical replication launcher", "system.process.command": "postgres: 10/main: bgworker: logical replication launcher", "system.process.cpu.percent": 0, "system.process.handles": 74, "system.process.id": 1577, "system.process.io.bytes.per.sec": -1025, "system.process.memory.used.bytes": 4263936, "system.process.memory.used.percent": 0.1, "system.process.name": "postgres", "system.process.started.time": " 141 days 22 hours 28 minutes 54 seconds", "system.process.started.time.seconds": 12263334, "system.process.threads": 1, "system.process.user": "postgres", "system.process.virtual.memory.bytes": 326762496}, {"status": "Up", "system.process": "apache2|/usr/sbin/apache2 -k start", "system.process.command": "/usr/sbin/apache2 -k start", "system.process.cpu.percent": 0, "system.process.handles": 49, "system.process.id": 1411, "system.process.io.bytes.per.sec": -1025, "system.process.memory.used.bytes": 4976640, "system.process.memory.used.percent": 0.1, "system.process.name": "apache2", "system.process.started.time": " 141 days 22 hours 29 minutes 3 seconds", "system.process.started.time.seconds": 12263343, "system.process.threads": 1, "system.process.user": "root", "system.process.virtual.memory.bytes": 80080896}, {"status": "Up", "system.process": "postgres|/usr/lib/postgresql/10/bin/postgres -D /var/lib/postgresql/10/main -c config_file=/etc/postgresql/10/main/postgresql.conf", "system.process.command": "/usr/lib/postgresql/10/bin/postgres -D /var/lib/postgresql/10/main -c config_file=/etc/postgresql/10/main/postgresql.conf", "system.process.cpu.percent": 0, "system.process.handles": 74, "system.process.id": 1485, "system.process.io.bytes.per.sec": -1025, "system.process.memory.used.bytes": 21106688, "system.process.memory.used.percent": 0.5, "system.process.name": "postgres", "system.process.started.time": " 141 days 22 hours 28 minutes 59 seconds", "system.process.started.time.seconds": 12263339, "system.process.threads": 1, "system.process.user": "postgres", "system.process.virtual.memory.bytes": 326471680}, {"status": "Up", "system.process": "postgres|postgres: 10/main: checkpointer process", "system.process.command": "postgres: 10/main: checkpointer process", "system.process.cpu.percent": 0, "system.process.handles": 73, "system.process.id": 1570, "system.process.memory.used.bytes": 3604480, "system.process.memory.used.percent": 0, "system.process.name": "postgres", "system.process.started.time": " 141 days 22 hours 28 minutes 54 seconds", "system.process.started.time.seconds": 12263334, "system.process.threads": 1, "system.process.user": "postgres", "system.process.virtual.memory.bytes": 326471680}, {"status": "Up", "system.process": "postgres|postgres: 10/main: writer process", "system.process.command": "postgres: 10/main: writer process", "system.process.cpu.percent": 0, "system.process.handles": 73, "system.process.id": 1571, "system.process.memory.used.bytes": 5824512, "system.process.memory.used.percent": 0.1, "system.process.name": "postgres", "system.process.started.time": " 141 days 22 hours 28 minutes 54 seconds", "system.process.started.time.seconds": 12263334, "system.process.threads": 1, "system.process.user": "postgres", "system.process.virtual.memory.bytes": 326471680}, {"status": "Up", "system.process": "apache2|/usr/sbin/apache2 -k start", "system.process.command": "/usr/sbin/apache2 -k start", "system.process.cpu.percent": 0, "system.process.handles": 55, "system.process.id": 14730, "system.process.memory.used.bytes": 4673536, "system.process.memory.used.percent": 0.1, "system.process.name": "apache2", "system.process.started.time": " 0 day 22 hours 31 minutes 23 seconds", "system.process.started.time.seconds": 81083, "system.process.threads": 27, "system.process.user": "www-data", "system.process.virtual.memory.bytes": 2058412032}, {"status": "Up", "system.process": "apache2|/usr/sbin/apache2 -k start", "system.process.command": "/usr/sbin/apache2 -k start", "system.process.cpu.percent": 0, "system.process.handles": 55, "system.process.id": 14731, "system.process.memory.used.bytes": 4673536, "system.process.memory.used.percent": 0.1, "system.process.name": "apache2", "system.process.started.time": " 0 day 22 hours 31 minutes 23 seconds", "system.process.started.time.seconds": 81083, "system.process.threads": 27, "system.process.user": "www-data", "system.process.virtual.memory.bytes": 2058412032}, {"status": "Up", "system.process": "postgres|postgres: 10/main: wal writer process", "system.process.command": "postgres: 10/main: wal writer process", "system.process.cpu.percent": 0, "system.process.handles": 74, "system.process.id": 1572, "system.process.io.bytes.per.sec": -1025, "system.process.memory.used.bytes": 8974336, "system.process.memory.used.percent": 0.2, "system.process.name": "postgres", "system.process.started.time": " 141 days 22 hours 28 minutes 54 seconds", "system.process.started.time.seconds": 12263334, "system.process.threads": 1, "system.process.user": "postgres", "system.process.virtual.memory.bytes": 326471680}], "system.process.network.connection": [{"system.process": "postgres|/usr/lib/postgresql/10/bin/postgres -D /var/lib/postgresql/10/main -c config_file=/etc/postgresql/10/main/postgresql.conf", "system.process.destination.ip": "************", "system.process.destination.port": "*", "system.process.source.ip": "************", "system.process.source.port": "5432"}]}, {"correlation.metrics": ["system.process.network.connection"], "system.process": [{"status": "Up", "system.process": "apache2|/usr/sbin/apache2 -k start", "system.process.command": "/usr/sbin/apache2 -k start", "system.process.cpu.percent": 0, "system.process.handles": 49, "system.process.id": 1411, "system.process.io.bytes.per.sec": -1025, "system.process.memory.used.bytes": 4976640, "system.process.memory.used.percent": 0.1, "system.process.name": "apache2", "system.process.started.time": " 141 days 22 hours 31 minutes 6 seconds", "system.process.started.time.seconds": 12263466, "system.process.threads": 1, "system.process.user": "root", "system.process.virtual.memory.bytes": 80080896}, {"status": "Up", "system.process": "postgres|/usr/lib/postgresql/10/bin/postgres -D /var/lib/postgresql/10/main -c config_file=/etc/postgresql/10/main/postgresql.conf", "system.process.command": "/usr/lib/postgresql/10/bin/postgres -D /var/lib/postgresql/10/main -c config_file=/etc/postgresql/10/main/postgresql.conf", "system.process.cpu.percent": 0, "system.process.handles": 74, "system.process.id": 1485, "system.process.io.bytes.per.sec": -1025, "system.process.memory.used.bytes": 21106688, "system.process.memory.used.percent": 0.5, "system.process.name": "postgres", "system.process.started.time": " 141 days 22 hours 31 minutes 2 seconds", "system.process.started.time.seconds": 12263462, "system.process.threads": 1, "system.process.user": "postgres", "system.process.virtual.memory.bytes": 326471680}, {"status": "Up", "system.process": "postgres|postgres: 10/main: checkpointer process", "system.process.command": "postgres: 10/main: checkpointer process", "system.process.cpu.percent": 0, "system.process.handles": 73, "system.process.id": 1570, "system.process.memory.used.bytes": 3604480, "system.process.memory.used.percent": 0, "system.process.name": "postgres", "system.process.started.time": " 141 days 22 hours 30 minutes 57 seconds", "system.process.started.time.seconds": 12263457, "system.process.threads": 1, "system.process.user": "postgres", "system.process.virtual.memory.bytes": 326471680}, {"status": "Up", "system.process": "apache2|/usr/sbin/apache2 -k start", "system.process.command": "/usr/sbin/apache2 -k start", "system.process.cpu.percent": 0, "system.process.handles": 55, "system.process.id": 14730, "system.process.memory.used.bytes": 4673536, "system.process.memory.used.percent": 0.1, "system.process.name": "apache2", "system.process.started.time": " 0 day 22 hours 33 minutes 26 seconds", "system.process.started.time.seconds": 81206, "system.process.threads": 27, "system.process.user": "www-data", "system.process.virtual.memory.bytes": 2058412032}, {"status": "Up", "system.process": "apache2|/usr/sbin/apache2 -k start", "system.process.command": "/usr/sbin/apache2 -k start", "system.process.cpu.percent": 0, "system.process.handles": 55, "system.process.id": 14731, "system.process.memory.used.bytes": 4673536, "system.process.memory.used.percent": 0.1, "system.process.name": "apache2", "system.process.started.time": " 0 day 22 hours 33 minutes 26 seconds", "system.process.started.time.seconds": 81206, "system.process.threads": 27, "system.process.user": "www-data", "system.process.virtual.memory.bytes": 2058412032}, {"status": "Up", "system.process": "postgres|postgres: 10/main: writer process", "system.process.command": "postgres: 10/main: writer process", "system.process.cpu.percent": 0, "system.process.handles": 73, "system.process.id": 1571, "system.process.memory.used.bytes": 5824512, "system.process.memory.used.percent": 0.1, "system.process.name": "postgres", "system.process.started.time": " 141 days 22 hours 30 minutes 57 seconds", "system.process.started.time.seconds": 12263457, "system.process.threads": 1, "system.process.user": "postgres", "system.process.virtual.memory.bytes": 326471680}, {"status": "Up", "system.process": "postgres|postgres: 10/main: wal writer process", "system.process.command": "postgres: 10/main: wal writer process", "system.process.cpu.percent": 0, "system.process.handles": 74, "system.process.id": 1572, "system.process.io.bytes.per.sec": -1025, "system.process.memory.used.bytes": 8974336, "system.process.memory.used.percent": 0.2, "system.process.name": "postgres", "system.process.started.time": " 141 days 22 hours 30 minutes 57 seconds", "system.process.started.time.seconds": 12263457, "system.process.threads": 1, "system.process.user": "postgres", "system.process.virtual.memory.bytes": 326471680}, {"status": "Up", "system.process": "postgres|postgres: 10/main: autovacuum launcher process", "system.process.command": "postgres: 10/main: autovacuum launcher process", "system.process.cpu.percent": 0, "system.process.handles": 74, "system.process.id": 1573, "system.process.io.bytes.per.sec": -1025, "system.process.memory.used.bytes": 6205440, "system.process.memory.used.percent": 0.1, "system.process.name": "postgres", "system.process.started.time": " 141 days 22 hours 30 minutes 57 seconds", "system.process.started.time.seconds": 12263457, "system.process.threads": 1, "system.process.user": "postgres", "system.process.virtual.memory.bytes": 326885376}, {"status": "Up", "system.process": "postgres|postgres: 10/main: stats collector process", "system.process.command": "postgres: 10/main: stats collector process", "system.process.cpu.percent": 0, "system.process.handles": 70, "system.process.id": 1576, "system.process.memory.used.bytes": 3063808, "system.process.memory.used.percent": 0, "system.process.name": "postgres", "system.process.started.time": " 141 days 22 hours 30 minutes 57 seconds", "system.process.started.time.seconds": 12263457, "system.process.threads": 1, "system.process.user": "postgres", "system.process.virtual.memory.bytes": 177905664}, {"status": "Up", "system.process": "postgres|postgres: 10/main: bgworker: logical replication launcher", "system.process.command": "postgres: 10/main: bgworker: logical replication launcher", "system.process.cpu.percent": 0, "system.process.handles": 74, "system.process.id": 1577, "system.process.io.bytes.per.sec": -1025, "system.process.memory.used.bytes": 4263936, "system.process.memory.used.percent": 0.1, "system.process.name": "postgres", "system.process.started.time": " 141 days 22 hours 30 minutes 57 seconds", "system.process.started.time.seconds": 12263457, "system.process.threads": 1, "system.process.user": "postgres", "system.process.virtual.memory.bytes": 326762496}], "system.process.network.connection": [{"system.process": "postgres|/usr/lib/postgresql/10/bin/postgres -D /var/lib/postgresql/10/main -c config_file=/etc/postgresql/10/main/postgresql.conf", "system.process.destination.ip": "************", "system.process.destination.port": "*", "system.process.source.ip": "************", "system.process.source.port": "5432"}]}, {"correlation.metrics": ["system.process.network.connection"], "system.process": [{"status": "Up", "system.process": "postgres|/usr/lib/postgresql/10/bin/postgres -D /var/lib/postgresql/10/main -c config_file=/etc/postgresql/10/main/postgresql.conf", "system.process.command": "/usr/lib/postgresql/10/bin/postgres -D /var/lib/postgresql/10/main -c config_file=/etc/postgresql/10/main/postgresql.conf", "system.process.cpu.percent": 0, "system.process.handles": 74, "system.process.id": 1485, "system.process.io.bytes.per.sec": -1025, "system.process.memory.used.bytes": 21106688, "system.process.memory.used.percent": 0.5, "system.process.name": "postgres", "system.process.started.time": " 141 days 22 hours 36 minutes 4 seconds", "system.process.started.time.seconds": 12263764, "system.process.threads": 1, "system.process.user": "postgres", "system.process.virtual.memory.bytes": 326471680}, {"status": "Up", "system.process": "postgres|postgres: 10/main: checkpointer process", "system.process.command": "postgres: 10/main: checkpointer process", "system.process.cpu.percent": 0, "system.process.handles": 73, "system.process.id": 1570, "system.process.memory.used.bytes": 3604480, "system.process.memory.used.percent": 0, "system.process.name": "postgres", "system.process.started.time": " 141 days 22 hours 35 minutes 59 seconds", "system.process.started.time.seconds": 12263759, "system.process.threads": 1, "system.process.user": "postgres", "system.process.virtual.memory.bytes": 326471680}, {"status": "Up", "system.process": "postgres|postgres: 10/main: writer process", "system.process.command": "postgres: 10/main: writer process", "system.process.cpu.percent": 0, "system.process.handles": 73, "system.process.id": 1571, "system.process.memory.used.bytes": 5824512, "system.process.memory.used.percent": 0.1, "system.process.name": "postgres", "system.process.started.time": " 141 days 22 hours 35 minutes 59 seconds", "system.process.started.time.seconds": 12263759, "system.process.threads": 1, "system.process.user": "postgres", "system.process.virtual.memory.bytes": 326471680}, {"status": "Up", "system.process": "postgres|postgres: 10/main: stats collector process", "system.process.command": "postgres: 10/main: stats collector process", "system.process.cpu.percent": 0, "system.process.handles": 70, "system.process.id": 1576, "system.process.memory.used.bytes": 3063808, "system.process.memory.used.percent": 0, "system.process.name": "postgres", "system.process.started.time": " 141 days 22 hours 35 minutes 59 seconds", "system.process.started.time.seconds": 12263759, "system.process.threads": 1, "system.process.user": "postgres", "system.process.virtual.memory.bytes": 177905664}, {"status": "Up", "system.process": "apache2|/usr/sbin/apache2 -k start", "system.process.command": "/usr/sbin/apache2 -k start", "system.process.cpu.percent": 0, "system.process.handles": 49, "system.process.id": 1411, "system.process.io.bytes.per.sec": -1025, "system.process.memory.used.bytes": 4976640, "system.process.memory.used.percent": 0.1, "system.process.name": "apache2", "system.process.started.time": " 141 days 22 hours 36 minutes 8 seconds", "system.process.started.time.seconds": 12263768, "system.process.threads": 1, "system.process.user": "root", "system.process.virtual.memory.bytes": 80080896}, {"status": "Up", "system.process": "postgres|postgres: 10/main: wal writer process", "system.process.command": "postgres: 10/main: wal writer process", "system.process.cpu.percent": 0, "system.process.handles": 74, "system.process.id": 1572, "system.process.io.bytes.per.sec": -1025, "system.process.memory.used.bytes": 8974336, "system.process.memory.used.percent": 0.2, "system.process.name": "postgres", "system.process.started.time": " 141 days 22 hours 35 minutes 59 seconds", "system.process.started.time.seconds": 12263759, "system.process.threads": 1, "system.process.user": "postgres", "system.process.virtual.memory.bytes": 326471680}, {"status": "Up", "system.process": "postgres|postgres: 10/main: autovacuum launcher process", "system.process.command": "postgres: 10/main: autovacuum launcher process", "system.process.cpu.percent": 0, "system.process.handles": 74, "system.process.id": 1573, "system.process.io.bytes.per.sec": -1025, "system.process.memory.used.bytes": 6205440, "system.process.memory.used.percent": 0.1, "system.process.name": "postgres", "system.process.started.time": " 141 days 22 hours 35 minutes 59 seconds", "system.process.started.time.seconds": 12263759, "system.process.threads": 1, "system.process.user": "postgres", "system.process.virtual.memory.bytes": 326885376}, {"status": "Up", "system.process": "postgres|postgres: 10/main: bgworker: logical replication launcher", "system.process.command": "postgres: 10/main: bgworker: logical replication launcher", "system.process.cpu.percent": 0, "system.process.handles": 74, "system.process.id": 1577, "system.process.io.bytes.per.sec": -1025, "system.process.memory.used.bytes": 4263936, "system.process.memory.used.percent": 0.1, "system.process.name": "postgres", "system.process.started.time": " 141 days 22 hours 35 minutes 59 seconds", "system.process.started.time.seconds": 12263759, "system.process.threads": 1, "system.process.user": "postgres", "system.process.virtual.memory.bytes": 326762496}, {"status": "Up", "system.process": "apache2|/usr/sbin/apache2 -k start", "system.process.command": "/usr/sbin/apache2 -k start", "system.process.cpu.percent": 0, "system.process.handles": 55, "system.process.id": 14730, "system.process.memory.used.bytes": 4673536, "system.process.memory.used.percent": 0.1, "system.process.name": "apache2", "system.process.started.time": " 0 day 22 hours 38 minutes 28 seconds", "system.process.started.time.seconds": 81508, "system.process.threads": 27, "system.process.user": "www-data", "system.process.virtual.memory.bytes": 2058412032}, {"status": "Up", "system.process": "apache2|/usr/sbin/apache2 -k start", "system.process.command": "/usr/sbin/apache2 -k start", "system.process.cpu.percent": 0, "system.process.handles": 55, "system.process.id": 14731, "system.process.memory.used.bytes": 4673536, "system.process.memory.used.percent": 0.1, "system.process.name": "apache2", "system.process.started.time": " 0 day 22 hours 38 minutes 28 seconds", "system.process.started.time.seconds": 81508, "system.process.threads": 27, "system.process.user": "www-data", "system.process.virtual.memory.bytes": 2058412032}], "system.process.network.connection": [{"system.process": "postgres|/usr/lib/postgresql/10/bin/postgres -D /var/lib/postgresql/10/main -c config_file=/etc/postgresql/10/main/postgresql.conf", "system.process.destination.ip": "************", "system.process.destination.port": "*", "system.process.source.ip": "************", "system.process.source.port": "5432"}]}, {"correlation.metrics": ["system.process.network.connection"], "system.process": [{"status": "Up", "system.process": "postgres|/usr/lib/postgresql/10/bin/postgres -D /var/lib/postgresql/10/main -c config_file=/etc/postgresql/10/main/postgresql.conf", "system.process.command": "/usr/lib/postgresql/10/bin/postgres -D /var/lib/postgresql/10/main -c config_file=/etc/postgresql/10/main/postgresql.conf", "system.process.cpu.percent": 0, "system.process.handles": 74, "system.process.id": 1485, "system.process.io.bytes.per.sec": -1025, "system.process.memory.used.bytes": 21106688, "system.process.memory.used.percent": 0.5, "system.process.name": "postgres", "system.process.started.time": " 141 days 22 hours 41 minutes 2 seconds", "system.process.started.time.seconds": 12264062, "system.process.threads": 1, "system.process.user": "postgres", "system.process.virtual.memory.bytes": 326471680}, {"status": "Up", "system.process": "postgres|postgres: 10/main: bgworker: logical replication launcher", "system.process.command": "postgres: 10/main: bgworker: logical replication launcher", "system.process.cpu.percent": 0, "system.process.handles": 74, "system.process.id": 1577, "system.process.io.bytes.per.sec": -1025, "system.process.memory.used.bytes": 4263936, "system.process.memory.used.percent": 0.1, "system.process.name": "postgres", "system.process.started.time": " 141 days 22 hours 40 minutes 57 seconds", "system.process.started.time.seconds": 12264057, "system.process.threads": 1, "system.process.user": "postgres", "system.process.virtual.memory.bytes": 326762496}, {"status": "Up", "system.process": "apache2|/usr/sbin/apache2 -k start", "system.process.command": "/usr/sbin/apache2 -k start", "system.process.cpu.percent": 0, "system.process.handles": 55, "system.process.id": 14731, "system.process.memory.used.bytes": 4673536, "system.process.memory.used.percent": 0.1, "system.process.name": "apache2", "system.process.started.time": " 0 day 22 hours 43 minutes 26 seconds", "system.process.started.time.seconds": 81806, "system.process.threads": 27, "system.process.user": "www-data", "system.process.virtual.memory.bytes": 2058412032}, {"status": "Up", "system.process": "apache2|/usr/sbin/apache2 -k start", "system.process.command": "/usr/sbin/apache2 -k start", "system.process.cpu.percent": 0, "system.process.handles": 49, "system.process.id": 1411, "system.process.io.bytes.per.sec": -1025, "system.process.memory.used.bytes": 4976640, "system.process.memory.used.percent": 0.1, "system.process.name": "apache2", "system.process.started.time": " 141 days 22 hours 41 minutes 6 seconds", "system.process.started.time.seconds": 12264066, "system.process.threads": 1, "system.process.user": "root", "system.process.virtual.memory.bytes": 80080896}, {"status": "Up", "system.process": "postgres|postgres: 10/main: checkpointer process", "system.process.command": "postgres: 10/main: checkpointer process", "system.process.cpu.percent": 0, "system.process.handles": 73, "system.process.id": 1570, "system.process.memory.used.bytes": 3604480, "system.process.memory.used.percent": 0, "system.process.name": "postgres", "system.process.started.time": " 141 days 22 hours 40 minutes 57 seconds", "system.process.started.time.seconds": 12264057, "system.process.threads": 1, "system.process.user": "postgres", "system.process.virtual.memory.bytes": 326471680}, {"status": "Up", "system.process": "postgres|postgres: 10/main: writer process", "system.process.command": "postgres: 10/main: writer process", "system.process.cpu.percent": 0, "system.process.handles": 73, "system.process.id": 1571, "system.process.memory.used.bytes": 5824512, "system.process.memory.used.percent": 0.1, "system.process.name": "postgres", "system.process.started.time": " 141 days 22 hours 40 minutes 57 seconds", "system.process.started.time.seconds": 12264057, "system.process.threads": 1, "system.process.user": "postgres", "system.process.virtual.memory.bytes": 326471680}, {"status": "Up", "system.process": "postgres|postgres: 10/main: wal writer process", "system.process.command": "postgres: 10/main: wal writer process", "system.process.cpu.percent": 0, "system.process.handles": 74, "system.process.id": 1572, "system.process.io.bytes.per.sec": -1025, "system.process.memory.used.bytes": 8974336, "system.process.memory.used.percent": 0.2, "system.process.name": "postgres", "system.process.started.time": " 141 days 22 hours 40 minutes 57 seconds", "system.process.started.time.seconds": 12264057, "system.process.threads": 1, "system.process.user": "postgres", "system.process.virtual.memory.bytes": 326471680}, {"status": "Up", "system.process": "postgres|postgres: 10/main: autovacuum launcher process", "system.process.command": "postgres: 10/main: autovacuum launcher process", "system.process.cpu.percent": 0, "system.process.handles": 74, "system.process.id": 1573, "system.process.io.bytes.per.sec": -1025, "system.process.memory.used.bytes": 6205440, "system.process.memory.used.percent": 0.1, "system.process.name": "postgres", "system.process.started.time": " 141 days 22 hours 40 minutes 57 seconds", "system.process.started.time.seconds": 12264057, "system.process.threads": 1, "system.process.user": "postgres", "system.process.virtual.memory.bytes": 326885376}, {"status": "Up", "system.process": "postgres|postgres: 10/main: stats collector process", "system.process.command": "postgres: 10/main: stats collector process", "system.process.cpu.percent": 0, "system.process.handles": 70, "system.process.id": 1576, "system.process.memory.used.bytes": 3063808, "system.process.memory.used.percent": 0, "system.process.name": "postgres", "system.process.started.time": " 141 days 22 hours 40 minutes 57 seconds", "system.process.started.time.seconds": 12264057, "system.process.threads": 1, "system.process.user": "postgres", "system.process.virtual.memory.bytes": 177905664}, {"status": "Up", "system.process": "apache2|/usr/sbin/apache2 -k start", "system.process.command": "/usr/sbin/apache2 -k start", "system.process.cpu.percent": 0, "system.process.handles": 55, "system.process.id": 14730, "system.process.memory.used.bytes": 4673536, "system.process.memory.used.percent": 0.1, "system.process.name": "apache2", "system.process.started.time": " 0 day 22 hours 43 minutes 26 seconds", "system.process.started.time.seconds": 81806, "system.process.threads": 27, "system.process.user": "www-data", "system.process.virtual.memory.bytes": 2058412032}], "system.process.network.connection": [{"system.process": "postgres|/usr/lib/postgresql/10/bin/postgres -D /var/lib/postgresql/10/main -c config_file=/etc/postgresql/10/main/postgresql.conf", "system.process.destination.ip": "************", "system.process.destination.port": "*", "system.process.source.ip": "************", "system.process.source.port": "5432"}]}, {"correlation.metrics": ["system.process.network.connection"], "system.process": [{"status": "Up", "system.process": "apache2|/usr/sbin/apache2 -k start", "system.process.command": "/usr/sbin/apache2 -k start", "system.process.cpu.percent": 0, "system.process.handles": 55, "system.process.id": 14730, "system.process.memory.used.bytes": 4673536, "system.process.memory.used.percent": 0.1, "system.process.name": "apache2", "system.process.started.time": " 0 day 22 hours 48 minutes 29 seconds", "system.process.started.time.seconds": 82109, "system.process.threads": 27, "system.process.user": "www-data", "system.process.virtual.memory.bytes": 2058412032}, {"status": "Up", "system.process": "postgres|postgres: 10/main: checkpointer process", "system.process.command": "postgres: 10/main: checkpointer process", "system.process.cpu.percent": 0, "system.process.handles": 73, "system.process.id": 1570, "system.process.memory.used.bytes": 3604480, "system.process.memory.used.percent": 0, "system.process.name": "postgres", "system.process.started.time": " 141 days 22 hours 46 minutes 0 second", "system.process.started.time.seconds": 12264360, "system.process.threads": 1, "system.process.user": "postgres", "system.process.virtual.memory.bytes": 326471680}, {"status": "Up", "system.process": "postgres|postgres: 10/main: writer process", "system.process.command": "postgres: 10/main: writer process", "system.process.cpu.percent": 0, "system.process.handles": 73, "system.process.id": 1571, "system.process.memory.used.bytes": 5824512, "system.process.memory.used.percent": 0.1, "system.process.name": "postgres", "system.process.started.time": " 141 days 22 hours 46 minutes 0 second", "system.process.started.time.seconds": 12264360, "system.process.threads": 1, "system.process.user": "postgres", "system.process.virtual.memory.bytes": 326471680}, {"status": "Up", "system.process": "postgres|postgres: 10/main: wal writer process", "system.process.command": "postgres: 10/main: wal writer process", "system.process.cpu.percent": 0, "system.process.handles": 74, "system.process.id": 1572, "system.process.io.bytes.per.sec": -1025, "system.process.memory.used.bytes": 8974336, "system.process.memory.used.percent": 0.2, "system.process.name": "postgres", "system.process.started.time": " 141 days 22 hours 46 minutes 0 second", "system.process.started.time.seconds": 12264360, "system.process.threads": 1, "system.process.user": "postgres", "system.process.virtual.memory.bytes": 326471680}, {"status": "Up", "system.process": "postgres|postgres: 10/main: stats collector process", "system.process.command": "postgres: 10/main: stats collector process", "system.process.cpu.percent": 0, "system.process.handles": 70, "system.process.id": 1576, "system.process.memory.used.bytes": 3063808, "system.process.memory.used.percent": 0, "system.process.name": "postgres", "system.process.started.time": " 141 days 22 hours 46 minutes 0 second", "system.process.started.time.seconds": 12264360, "system.process.threads": 1, "system.process.user": "postgres", "system.process.virtual.memory.bytes": 177905664}, {"status": "Up", "system.process": "apache2|/usr/sbin/apache2 -k start", "system.process.command": "/usr/sbin/apache2 -k start", "system.process.cpu.percent": 0, "system.process.handles": 55, "system.process.id": 14731, "system.process.memory.used.bytes": 4673536, "system.process.memory.used.percent": 0.1, "system.process.name": "apache2", "system.process.started.time": " 0 day 22 hours 48 minutes 29 seconds", "system.process.started.time.seconds": 82109, "system.process.threads": 27, "system.process.user": "www-data", "system.process.virtual.memory.bytes": 2058412032}, {"status": "Up", "system.process": "apache2|/usr/sbin/apache2 -k start", "system.process.command": "/usr/sbin/apache2 -k start", "system.process.cpu.percent": 0, "system.process.handles": 49, "system.process.id": 1411, "system.process.io.bytes.per.sec": -1025, "system.process.memory.used.bytes": 4976640, "system.process.memory.used.percent": 0.1, "system.process.name": "apache2", "system.process.started.time": " 141 days 22 hours 46 minutes 9 seconds", "system.process.started.time.seconds": 12264369, "system.process.threads": 1, "system.process.user": "root", "system.process.virtual.memory.bytes": 80080896}, {"status": "Up", "system.process": "postgres|/usr/lib/postgresql/10/bin/postgres -D /var/lib/postgresql/10/main -c config_file=/etc/postgresql/10/main/postgresql.conf", "system.process.command": "/usr/lib/postgresql/10/bin/postgres -D /var/lib/postgresql/10/main -c config_file=/etc/postgresql/10/main/postgresql.conf", "system.process.cpu.percent": 0, "system.process.handles": 74, "system.process.id": 1485, "system.process.io.bytes.per.sec": -1025, "system.process.memory.used.bytes": 21106688, "system.process.memory.used.percent": 0.5, "system.process.name": "postgres", "system.process.started.time": " 141 days 22 hours 46 minutes 5 seconds", "system.process.started.time.seconds": 12264365, "system.process.threads": 1, "system.process.user": "postgres", "system.process.virtual.memory.bytes": 326471680}, {"status": "Up", "system.process": "postgres|postgres: 10/main: autovacuum launcher process", "system.process.command": "postgres: 10/main: autovacuum launcher process", "system.process.cpu.percent": 0, "system.process.handles": 74, "system.process.id": 1573, "system.process.io.bytes.per.sec": -1025, "system.process.memory.used.bytes": 6205440, "system.process.memory.used.percent": 0.1, "system.process.name": "postgres", "system.process.started.time": " 141 days 22 hours 46 minutes 0 second", "system.process.started.time.seconds": 12264360, "system.process.threads": 1, "system.process.user": "postgres", "system.process.virtual.memory.bytes": 326885376}, {"status": "Up", "system.process": "postgres|postgres: 10/main: bgworker: logical replication launcher", "system.process.command": "postgres: 10/main: bgworker: logical replication launcher", "system.process.cpu.percent": 0, "system.process.handles": 74, "system.process.id": 1577, "system.process.io.bytes.per.sec": -1025, "system.process.memory.used.bytes": 4263936, "system.process.memory.used.percent": 0.1, "system.process.name": "postgres", "system.process.started.time": " 141 days 22 hours 46 minutes 0 second", "system.process.started.time.seconds": 12264360, "system.process.threads": 1, "system.process.user": "postgres", "system.process.virtual.memory.bytes": 326762496}], "system.process.network.connection": [{"system.process": "postgres|/usr/lib/postgresql/10/bin/postgres -D /var/lib/postgresql/10/main -c config_file=/etc/postgresql/10/main/postgresql.conf", "system.process.destination.ip": "************", "system.process.destination.port": "*", "system.process.source.ip": "************", "system.process.source.port": "5432"}]}, {"correlation.metrics": ["system.process.network.connection"], "system.process": [{"status": "Up", "system.process": "apache2|/usr/sbin/apache2 -k start", "system.process.command": "/usr/sbin/apache2 -k start", "system.process.cpu.percent": 0, "system.process.handles": 49, "system.process.id": 1411, "system.process.io.bytes.per.sec": -1025, "system.process.memory.used.bytes": 4976640, "system.process.memory.used.percent": 0.1, "system.process.name": "apache2", "system.process.started.time": " 141 days 22 hours 51 minutes 6 seconds", "system.process.started.time.seconds": 12264666, "system.process.threads": 1, "system.process.user": "root", "system.process.virtual.memory.bytes": 80080896}, {"status": "Up", "system.process": "postgres|/usr/lib/postgresql/10/bin/postgres -D /var/lib/postgresql/10/main -c config_file=/etc/postgresql/10/main/postgresql.conf", "system.process.command": "/usr/lib/postgresql/10/bin/postgres -D /var/lib/postgresql/10/main -c config_file=/etc/postgresql/10/main/postgresql.conf", "system.process.cpu.percent": 0, "system.process.handles": 74, "system.process.id": 1485, "system.process.io.bytes.per.sec": -1025, "system.process.memory.used.bytes": 21106688, "system.process.memory.used.percent": 0.5, "system.process.name": "postgres", "system.process.started.time": " 141 days 22 hours 51 minutes 2 seconds", "system.process.started.time.seconds": 12264662, "system.process.threads": 1, "system.process.user": "postgres", "system.process.virtual.memory.bytes": 326471680}, {"status": "Up", "system.process": "postgres|postgres: 10/main: writer process", "system.process.command": "postgres: 10/main: writer process", "system.process.cpu.percent": 0, "system.process.handles": 73, "system.process.id": 1571, "system.process.memory.used.bytes": 5824512, "system.process.memory.used.percent": 0.1, "system.process.name": "postgres", "system.process.started.time": " 141 days 22 hours 50 minutes 57 seconds", "system.process.started.time.seconds": 12264657, "system.process.threads": 1, "system.process.user": "postgres", "system.process.virtual.memory.bytes": 326471680}, {"status": "Up", "system.process": "postgres|postgres: 10/main: stats collector process", "system.process.command": "postgres: 10/main: stats collector process", "system.process.cpu.percent": 0, "system.process.handles": 70, "system.process.id": 1576, "system.process.memory.used.bytes": 3063808, "system.process.memory.used.percent": 0, "system.process.name": "postgres", "system.process.started.time": " 141 days 22 hours 50 minutes 57 seconds", "system.process.started.time.seconds": 12264657, "system.process.threads": 1, "system.process.user": "postgres", "system.process.virtual.memory.bytes": 177905664}, {"status": "Up", "system.process": "postgres|postgres: 10/main: bgworker: logical replication launcher", "system.process.command": "postgres: 10/main: bgworker: logical replication launcher", "system.process.cpu.percent": 0, "system.process.handles": 74, "system.process.id": 1577, "system.process.io.bytes.per.sec": -1025, "system.process.memory.used.bytes": 4263936, "system.process.memory.used.percent": 0.1, "system.process.name": "postgres", "system.process.started.time": " 141 days 22 hours 50 minutes 57 seconds", "system.process.started.time.seconds": 12264657, "system.process.threads": 1, "system.process.user": "postgres", "system.process.virtual.memory.bytes": 326762496}, {"status": "Up", "system.process": "apache2|/usr/sbin/apache2 -k start", "system.process.command": "/usr/sbin/apache2 -k start", "system.process.cpu.percent": 0, "system.process.handles": 55, "system.process.id": 14730, "system.process.memory.used.bytes": 4673536, "system.process.memory.used.percent": 0.1, "system.process.name": "apache2", "system.process.started.time": " 0 day 22 hours 53 minutes 26 seconds", "system.process.started.time.seconds": 82406, "system.process.threads": 27, "system.process.user": "www-data", "system.process.virtual.memory.bytes": 2058412032}, {"status": "Up", "system.process": "apache2|/usr/sbin/apache2 -k start", "system.process.command": "/usr/sbin/apache2 -k start", "system.process.cpu.percent": 0, "system.process.handles": 55, "system.process.id": 14731, "system.process.memory.used.bytes": 4673536, "system.process.memory.used.percent": 0.1, "system.process.name": "apache2", "system.process.started.time": " 0 day 22 hours 53 minutes 26 seconds", "system.process.started.time.seconds": 82406, "system.process.threads": 27, "system.process.user": "www-data", "system.process.virtual.memory.bytes": 2058412032}, {"status": "Up", "system.process": "postgres|postgres: 10/main: checkpointer process", "system.process.command": "postgres: 10/main: checkpointer process", "system.process.cpu.percent": 0, "system.process.handles": 73, "system.process.id": 1570, "system.process.memory.used.bytes": 3604480, "system.process.memory.used.percent": 0, "system.process.name": "postgres", "system.process.started.time": " 141 days 22 hours 50 minutes 57 seconds", "system.process.started.time.seconds": 12264657, "system.process.threads": 1, "system.process.user": "postgres", "system.process.virtual.memory.bytes": 326471680}, {"status": "Up", "system.process": "postgres|postgres: 10/main: wal writer process", "system.process.command": "postgres: 10/main: wal writer process", "system.process.cpu.percent": 0, "system.process.handles": 74, "system.process.id": 1572, "system.process.io.bytes.per.sec": -1025, "system.process.memory.used.bytes": 8974336, "system.process.memory.used.percent": 0.2, "system.process.name": "postgres", "system.process.started.time": " 141 days 22 hours 50 minutes 57 seconds", "system.process.started.time.seconds": 12264657, "system.process.threads": 1, "system.process.user": "postgres", "system.process.virtual.memory.bytes": 326471680}, {"status": "Up", "system.process": "postgres|postgres: 10/main: autovacuum launcher process", "system.process.command": "postgres: 10/main: autovacuum launcher process", "system.process.cpu.percent": 0, "system.process.handles": 74, "system.process.id": 1573, "system.process.io.bytes.per.sec": -1025, "system.process.memory.used.bytes": 6205440, "system.process.memory.used.percent": 0.1, "system.process.name": "postgres", "system.process.started.time": " 141 days 22 hours 50 minutes 57 seconds", "system.process.started.time.seconds": 12264657, "system.process.threads": 1, "system.process.user": "postgres", "system.process.virtual.memory.bytes": 326885376}], "system.process.network.connection": [{"system.process": "postgres|/usr/lib/postgresql/10/bin/postgres -D /var/lib/postgresql/10/main -c config_file=/etc/postgresql/10/main/postgresql.conf", "system.process.destination.ip": "************", "system.process.destination.port": "*", "system.process.source.ip": "************", "system.process.source.port": "5432"}]}, {"correlation.metrics": ["system.process.network.connection"], "system.process": [{"status": "Up", "system.process": "apache2|/usr/sbin/apache2 -k start", "system.process.command": "/usr/sbin/apache2 -k start", "system.process.cpu.percent": 0, "system.process.handles": 55, "system.process.id": 14730, "system.process.memory.used.bytes": 4673536, "system.process.memory.used.percent": 0.1, "system.process.name": "apache2", "system.process.started.time": " 0 day 22 hours 58 minutes 28 seconds", "system.process.started.time.seconds": 82708, "system.process.threads": 27, "system.process.user": "www-data", "system.process.virtual.memory.bytes": 2058412032}, {"status": "Up", "system.process": "apache2|/usr/sbin/apache2 -k start", "system.process.command": "/usr/sbin/apache2 -k start", "system.process.cpu.percent": 0, "system.process.handles": 49, "system.process.id": 1411, "system.process.io.bytes.per.sec": -1025, "system.process.memory.used.bytes": 4976640, "system.process.memory.used.percent": 0.1, "system.process.name": "apache2", "system.process.started.time": " 141 days 22 hours 56 minutes 8 seconds", "system.process.started.time.seconds": 12264968, "system.process.threads": 1, "system.process.user": "root", "system.process.virtual.memory.bytes": 80080896}, {"status": "Up", "system.process": "postgres|/usr/lib/postgresql/10/bin/postgres -D /var/lib/postgresql/10/main -c config_file=/etc/postgresql/10/main/postgresql.conf", "system.process.command": "/usr/lib/postgresql/10/bin/postgres -D /var/lib/postgresql/10/main -c config_file=/etc/postgresql/10/main/postgresql.conf", "system.process.cpu.percent": 0, "system.process.handles": 74, "system.process.id": 1485, "system.process.io.bytes.per.sec": -1025, "system.process.memory.used.bytes": 21106688, "system.process.memory.used.percent": 0.5, "system.process.name": "postgres", "system.process.started.time": " 141 days 22 hours 56 minutes 4 seconds", "system.process.started.time.seconds": 12264964, "system.process.threads": 1, "system.process.user": "postgres", "system.process.virtual.memory.bytes": 326471680}, {"status": "Up", "system.process": "postgres|postgres: 10/main: stats collector process", "system.process.command": "postgres: 10/main: stats collector process", "system.process.cpu.percent": 0, "system.process.handles": 70, "system.process.id": 1576, "system.process.memory.used.bytes": 3063808, "system.process.memory.used.percent": 0, "system.process.name": "postgres", "system.process.started.time": " 141 days 22 hours 55 minutes 59 seconds", "system.process.started.time.seconds": 12264959, "system.process.threads": 1, "system.process.user": "postgres", "system.process.virtual.memory.bytes": 177905664}, {"status": "Up", "system.process": "apache2|/usr/sbin/apache2 -k start", "system.process.command": "/usr/sbin/apache2 -k start", "system.process.cpu.percent": 0, "system.process.handles": 55, "system.process.id": 14731, "system.process.memory.used.bytes": 4673536, "system.process.memory.used.percent": 0.1, "system.process.name": "apache2", "system.process.started.time": " 0 day 22 hours 58 minutes 28 seconds", "system.process.started.time.seconds": 82708, "system.process.threads": 27, "system.process.user": "www-data", "system.process.virtual.memory.bytes": 2058412032}, {"status": "Up", "system.process": "postgres|postgres: 10/main: checkpointer process", "system.process.command": "postgres: 10/main: checkpointer process", "system.process.cpu.percent": 0, "system.process.handles": 73, "system.process.id": 1570, "system.process.memory.used.bytes": 3604480, "system.process.memory.used.percent": 0, "system.process.name": "postgres", "system.process.started.time": " 141 days 22 hours 55 minutes 59 seconds", "system.process.started.time.seconds": 12264959, "system.process.threads": 1, "system.process.user": "postgres", "system.process.virtual.memory.bytes": 326471680}, {"status": "Up", "system.process": "postgres|postgres: 10/main: writer process", "system.process.command": "postgres: 10/main: writer process", "system.process.cpu.percent": 0, "system.process.handles": 73, "system.process.id": 1571, "system.process.memory.used.bytes": 5824512, "system.process.memory.used.percent": 0.1, "system.process.name": "postgres", "system.process.started.time": " 141 days 22 hours 55 minutes 59 seconds", "system.process.started.time.seconds": 12264959, "system.process.threads": 1, "system.process.user": "postgres", "system.process.virtual.memory.bytes": 326471680}, {"status": "Up", "system.process": "postgres|postgres: 10/main: wal writer process", "system.process.command": "postgres: 10/main: wal writer process", "system.process.cpu.percent": 0, "system.process.handles": 74, "system.process.id": 1572, "system.process.io.bytes.per.sec": -1025, "system.process.memory.used.bytes": 8974336, "system.process.memory.used.percent": 0.2, "system.process.name": "postgres", "system.process.started.time": " 141 days 22 hours 55 minutes 59 seconds", "system.process.started.time.seconds": 12264959, "system.process.threads": 1, "system.process.user": "postgres", "system.process.virtual.memory.bytes": 326471680}, {"status": "Up", "system.process": "postgres|postgres: 10/main: autovacuum launcher process", "system.process.command": "postgres: 10/main: autovacuum launcher process", "system.process.cpu.percent": 0, "system.process.handles": 74, "system.process.id": 1573, "system.process.io.bytes.per.sec": -1025, "system.process.memory.used.bytes": 6205440, "system.process.memory.used.percent": 0.1, "system.process.name": "postgres", "system.process.started.time": " 141 days 22 hours 55 minutes 59 seconds", "system.process.started.time.seconds": 12264959, "system.process.threads": 1, "system.process.user": "postgres", "system.process.virtual.memory.bytes": 326885376}, {"status": "Up", "system.process": "postgres|postgres: 10/main: bgworker: logical replication launcher", "system.process.command": "postgres: 10/main: bgworker: logical replication launcher", "system.process.cpu.percent": 0, "system.process.handles": 74, "system.process.id": 1577, "system.process.io.bytes.per.sec": -1025, "system.process.memory.used.bytes": 4263936, "system.process.memory.used.percent": 0.1, "system.process.name": "postgres", "system.process.started.time": " 141 days 22 hours 55 minutes 59 seconds", "system.process.started.time.seconds": 12264959, "system.process.threads": 1, "system.process.user": "postgres", "system.process.virtual.memory.bytes": 326762496}], "system.process.network.connection": [{"system.process": "postgres|/usr/lib/postgresql/10/bin/postgres -D /var/lib/postgresql/10/main -c config_file=/etc/postgresql/10/main/postgresql.conf", "system.process.destination.ip": "************", "system.process.destination.port": "*", "system.process.source.ip": "************", "system.process.source.port": "5432"}]}, {"correlation.metrics": ["system.process.network.connection"], "system.process": [{"status": "Up", "system.process": "postgres|postgres: 10/main: wal writer process", "system.process.command": "postgres: 10/main: wal writer process", "system.process.cpu.percent": 0, "system.process.handles": 74, "system.process.id": 1572, "system.process.io.bytes.per.sec": -1025, "system.process.memory.used.bytes": 8974336, "system.process.memory.used.percent": 0.2, "system.process.name": "postgres", "system.process.started.time": " 141 days 23 hours 5 minutes 59 seconds", "system.process.started.time.seconds": 12265559, "system.process.threads": 1, "system.process.user": "postgres", "system.process.virtual.memory.bytes": 326471680}, {"status": "Up", "system.process": "postgres|postgres: 10/main: bgworker: logical replication launcher", "system.process.command": "postgres: 10/main: bgworker: logical replication launcher", "system.process.cpu.percent": 0, "system.process.handles": 74, "system.process.id": 1577, "system.process.io.bytes.per.sec": -1025, "system.process.memory.used.bytes": 4263936, "system.process.memory.used.percent": 0.1, "system.process.name": "postgres", "system.process.started.time": " 141 days 23 hours 5 minutes 59 seconds", "system.process.started.time.seconds": 12265559, "system.process.threads": 1, "system.process.user": "postgres", "system.process.virtual.memory.bytes": 326762496}, {"status": "Up", "system.process": "apache2|/usr/sbin/apache2 -k start", "system.process.command": "/usr/sbin/apache2 -k start", "system.process.cpu.percent": 0, "system.process.handles": 55, "system.process.id": 14730, "system.process.memory.used.bytes": 4673536, "system.process.memory.used.percent": 0.1, "system.process.name": "apache2", "system.process.started.time": " 0 day 23 hours 8 minutes 28 seconds", "system.process.started.time.seconds": 83308, "system.process.threads": 27, "system.process.user": "www-data", "system.process.virtual.memory.bytes": 2058412032}, {"status": "Up", "system.process": "apache2|/usr/sbin/apache2 -k start", "system.process.command": "/usr/sbin/apache2 -k start", "system.process.cpu.percent": 0, "system.process.handles": 55, "system.process.id": 14731, "system.process.memory.used.bytes": 4673536, "system.process.memory.used.percent": 0.1, "system.process.name": "apache2", "system.process.started.time": " 0 day 23 hours 8 minutes 28 seconds", "system.process.started.time.seconds": 83308, "system.process.threads": 27, "system.process.user": "www-data", "system.process.virtual.memory.bytes": 2058412032}, {"status": "Up", "system.process": "apache2|/usr/sbin/apache2 -k start", "system.process.command": "/usr/sbin/apache2 -k start", "system.process.cpu.percent": 0, "system.process.handles": 49, "system.process.id": 1411, "system.process.io.bytes.per.sec": -1025, "system.process.memory.used.bytes": 4976640, "system.process.memory.used.percent": 0.1, "system.process.name": "apache2", "system.process.started.time": " 141 days 23 hours 6 minutes 8 seconds", "system.process.started.time.seconds": 12265568, "system.process.threads": 1, "system.process.user": "root", "system.process.virtual.memory.bytes": 80080896}, {"status": "Up", "system.process": "postgres|/usr/lib/postgresql/10/bin/postgres -D /var/lib/postgresql/10/main -c config_file=/etc/postgresql/10/main/postgresql.conf", "system.process.command": "/usr/lib/postgresql/10/bin/postgres -D /var/lib/postgresql/10/main -c config_file=/etc/postgresql/10/main/postgresql.conf", "system.process.cpu.percent": 0, "system.process.handles": 74, "system.process.id": 1485, "system.process.io.bytes.per.sec": -1025, "system.process.memory.used.bytes": 21106688, "system.process.memory.used.percent": 0.5, "system.process.name": "postgres", "system.process.started.time": " 141 days 23 hours 6 minutes 4 seconds", "system.process.started.time.seconds": 12265564, "system.process.threads": 1, "system.process.user": "postgres", "system.process.virtual.memory.bytes": 326471680}, {"status": "Up", "system.process": "postgres|postgres: 10/main: checkpointer process", "system.process.command": "postgres: 10/main: checkpointer process", "system.process.cpu.percent": 0, "system.process.handles": 73, "system.process.id": 1570, "system.process.memory.used.bytes": 3604480, "system.process.memory.used.percent": 0, "system.process.name": "postgres", "system.process.started.time": " 141 days 23 hours 5 minutes 59 seconds", "system.process.started.time.seconds": 12265559, "system.process.threads": 1, "system.process.user": "postgres", "system.process.virtual.memory.bytes": 326471680}, {"status": "Up", "system.process": "postgres|postgres: 10/main: writer process", "system.process.command": "postgres: 10/main: writer process", "system.process.cpu.percent": 0, "system.process.handles": 73, "system.process.id": 1571, "system.process.memory.used.bytes": 5824512, "system.process.memory.used.percent": 0.1, "system.process.name": "postgres", "system.process.started.time": " 141 days 23 hours 5 minutes 59 seconds", "system.process.started.time.seconds": 12265559, "system.process.threads": 1, "system.process.user": "postgres", "system.process.virtual.memory.bytes": 326471680}, {"status": "Up", "system.process": "postgres|postgres: 10/main: autovacuum launcher process", "system.process.command": "postgres: 10/main: autovacuum launcher process", "system.process.cpu.percent": 0, "system.process.handles": 74, "system.process.id": 1573, "system.process.io.bytes.per.sec": -1025, "system.process.memory.used.bytes": 6205440, "system.process.memory.used.percent": 0.1, "system.process.name": "postgres", "system.process.started.time": " 141 days 23 hours 5 minutes 59 seconds", "system.process.started.time.seconds": 12265559, "system.process.threads": 1, "system.process.user": "postgres", "system.process.virtual.memory.bytes": 326885376}, {"status": "Up", "system.process": "postgres|postgres: 10/main: stats collector process", "system.process.command": "postgres: 10/main: stats collector process", "system.process.cpu.percent": 0, "system.process.handles": 70, "system.process.id": 1576, "system.process.memory.used.bytes": 3063808, "system.process.memory.used.percent": 0, "system.process.name": "postgres", "system.process.started.time": " 141 days 23 hours 5 minutes 59 seconds", "system.process.started.time.seconds": 12265559, "system.process.threads": 1, "system.process.user": "postgres", "system.process.virtual.memory.bytes": 177905664}], "system.process.network.connection": [{"system.process": "postgres|/usr/lib/postgresql/10/bin/postgres -D /var/lib/postgresql/10/main -c config_file=/etc/postgresql/10/main/postgresql.conf", "system.process.destination.ip": "************", "system.process.destination.port": "*", "system.process.source.ip": "************", "system.process.source.port": "5432"}]}], "rediscovery": null}, "************": {"discovery": null, "collect": [{"correlation.metrics": ["system.process.network.connection"], "system.process": [{"status": "Up", "system.process": "apache2|/usr/sbin/apache2 -k start", "system.process.command": "/usr/sbin/apache2 -k start", "system.process.cpu.percent": 0, "system.process.handles": 0, "system.process.id": 803, "system.process.memory.used.bytes": 4956160, "system.process.memory.used.percent": 0.4, "system.process.name": "apache2", "system.process.started.time": " 99 days 18 hours 7 minutes 15 seconds", "system.process.started.time.seconds": 8618835, "system.process.threads": 27, "system.process.user": "www-data", "system.process.virtual.memory.bytes": 1241468928}, {"status": "Up", "system.process": "apache2|/usr/sbin/apache2 -k start", "system.process.command": "/usr/sbin/apache2 -k start", "system.process.cpu.percent": 0, "system.process.handles": 0, "system.process.id": 804, "system.process.memory.used.bytes": 5206016, "system.process.memory.used.percent": 0.5, "system.process.name": "apache2", "system.process.started.time": " 99 days 18 hours 7 minutes 15 seconds", "system.process.started.time.seconds": 8618835, "system.process.threads": 27, "system.process.user": "www-data", "system.process.virtual.memory.bytes": 1241518080}, {"status": "Up", "system.process": "apache2|/usr/sbin/apache2 -k start", "system.process.command": "/usr/sbin/apache2 -k start", "system.process.cpu.percent": 0, "system.process.handles": 0, "system.process.id": 789, "system.process.memory.used.bytes": 2199552, "system.process.memory.used.percent": 0.2, "system.process.name": "apache2", "system.process.started.time": " 99 days 18 hours 7 minutes 16 seconds", "system.process.started.time.seconds": 8618836, "system.process.threads": 1, "system.process.user": "root", "system.process.virtual.memory.bytes": 6836224}]}, {"correlation.metrics": ["system.process.network.connection"], "system.process": [{"status": "Up", "system.process": "apache2|/usr/sbin/apache2 -k start", "system.process.command": "/usr/sbin/apache2 -k start", "system.process.cpu.percent": 0, "system.process.handles": 0, "system.process.id": 789, "system.process.memory.used.bytes": 2199552, "system.process.memory.used.percent": 0.2, "system.process.name": "apache2", "system.process.started.time": " 99 days 18 hours 9 minutes 20 seconds", "system.process.started.time.seconds": 8618960, "system.process.threads": 1, "system.process.user": "root", "system.process.virtual.memory.bytes": 6836224}, {"status": "Up", "system.process": "apache2|/usr/sbin/apache2 -k start", "system.process.command": "/usr/sbin/apache2 -k start", "system.process.cpu.percent": 0, "system.process.handles": 0, "system.process.id": 803, "system.process.memory.used.bytes": 4956160, "system.process.memory.used.percent": 0.4, "system.process.name": "apache2", "system.process.started.time": " 99 days 18 hours 9 minutes 19 seconds", "system.process.started.time.seconds": 8618959, "system.process.threads": 27, "system.process.user": "www-data", "system.process.virtual.memory.bytes": 1241468928}, {"status": "Up", "system.process": "apache2|/usr/sbin/apache2 -k start", "system.process.command": "/usr/sbin/apache2 -k start", "system.process.cpu.percent": 0, "system.process.handles": 0, "system.process.id": 804, "system.process.memory.used.bytes": 5206016, "system.process.memory.used.percent": 0.5, "system.process.name": "apache2", "system.process.started.time": " 99 days 18 hours 9 minutes 19 seconds", "system.process.started.time.seconds": 8618959, "system.process.threads": 27, "system.process.user": "www-data", "system.process.virtual.memory.bytes": 1241518080}]}, {"correlation.metrics": ["system.process.network.connection"], "system.process": [{"status": "Up", "system.process": "apache2|/usr/sbin/apache2 -k start", "system.process.command": "/usr/sbin/apache2 -k start", "system.process.cpu.percent": 0, "system.process.handles": 0, "system.process.id": 789, "system.process.memory.used.bytes": 2199552, "system.process.memory.used.percent": 0.2, "system.process.name": "apache2", "system.process.started.time": " 99 days 18 hours 14 minutes 20 seconds", "system.process.started.time.seconds": 8619260, "system.process.threads": 1, "system.process.user": "root", "system.process.virtual.memory.bytes": 6836224}, {"status": "Up", "system.process": "apache2|/usr/sbin/apache2 -k start", "system.process.command": "/usr/sbin/apache2 -k start", "system.process.cpu.percent": 0, "system.process.handles": 0, "system.process.id": 803, "system.process.memory.used.bytes": 4956160, "system.process.memory.used.percent": 0.4, "system.process.name": "apache2", "system.process.started.time": " 99 days 18 hours 14 minutes 19 seconds", "system.process.started.time.seconds": 8619259, "system.process.threads": 27, "system.process.user": "www-data", "system.process.virtual.memory.bytes": 1241468928}, {"status": "Up", "system.process": "apache2|/usr/sbin/apache2 -k start", "system.process.command": "/usr/sbin/apache2 -k start", "system.process.cpu.percent": 0, "system.process.handles": 0, "system.process.id": 804, "system.process.memory.used.bytes": 5206016, "system.process.memory.used.percent": 0.5, "system.process.name": "apache2", "system.process.started.time": " 99 days 18 hours 14 minutes 19 seconds", "system.process.started.time.seconds": 8619259, "system.process.threads": 27, "system.process.user": "www-data", "system.process.virtual.memory.bytes": 1241518080}]}, {"correlation.metrics": ["system.process.network.connection"], "system.process": [{"status": "Up", "system.process": "apache2|/usr/sbin/apache2 -k start", "system.process.command": "/usr/sbin/apache2 -k start", "system.process.cpu.percent": 0, "system.process.handles": 0, "system.process.id": 789, "system.process.memory.used.bytes": 2199552, "system.process.memory.used.percent": 0.2, "system.process.name": "apache2", "system.process.started.time": " 99 days 18 hours 19 minutes 20 seconds", "system.process.started.time.seconds": 8619560, "system.process.threads": 1, "system.process.user": "root", "system.process.virtual.memory.bytes": 6836224}, {"status": "Up", "system.process": "apache2|/usr/sbin/apache2 -k start", "system.process.command": "/usr/sbin/apache2 -k start", "system.process.cpu.percent": 0, "system.process.handles": 0, "system.process.id": 803, "system.process.memory.used.bytes": 4956160, "system.process.memory.used.percent": 0.4, "system.process.name": "apache2", "system.process.started.time": " 99 days 18 hours 19 minutes 19 seconds", "system.process.started.time.seconds": 8619559, "system.process.threads": 27, "system.process.user": "www-data", "system.process.virtual.memory.bytes": 1241468928}, {"status": "Up", "system.process": "apache2|/usr/sbin/apache2 -k start", "system.process.command": "/usr/sbin/apache2 -k start", "system.process.cpu.percent": 0, "system.process.handles": 0, "system.process.id": 804, "system.process.memory.used.bytes": 5206016, "system.process.memory.used.percent": 0.5, "system.process.name": "apache2", "system.process.started.time": " 99 days 18 hours 19 minutes 19 seconds", "system.process.started.time.seconds": 8619559, "system.process.threads": 27, "system.process.user": "www-data", "system.process.virtual.memory.bytes": 1241518080}]}, {"correlation.metrics": ["system.process.network.connection"], "system.process": [{"status": "Up", "system.process": "apache2|/usr/sbin/apache2 -k start", "system.process.command": "/usr/sbin/apache2 -k start", "system.process.cpu.percent": 0, "system.process.handles": 0, "system.process.id": 789, "system.process.memory.used.bytes": 2199552, "system.process.memory.used.percent": 0.2, "system.process.name": "apache2", "system.process.started.time": " 99 days 18 hours 24 minutes 20 seconds", "system.process.started.time.seconds": 8619860, "system.process.threads": 1, "system.process.user": "root", "system.process.virtual.memory.bytes": 6836224}, {"status": "Up", "system.process": "apache2|/usr/sbin/apache2 -k start", "system.process.command": "/usr/sbin/apache2 -k start", "system.process.cpu.percent": 0, "system.process.handles": 0, "system.process.id": 803, "system.process.memory.used.bytes": 4956160, "system.process.memory.used.percent": 0.4, "system.process.name": "apache2", "system.process.started.time": " 99 days 18 hours 24 minutes 19 seconds", "system.process.started.time.seconds": 8619859, "system.process.threads": 27, "system.process.user": "www-data", "system.process.virtual.memory.bytes": 1241468928}, {"status": "Up", "system.process": "apache2|/usr/sbin/apache2 -k start", "system.process.command": "/usr/sbin/apache2 -k start", "system.process.cpu.percent": 0, "system.process.handles": 0, "system.process.id": 804, "system.process.memory.used.bytes": 5206016, "system.process.memory.used.percent": 0.5, "system.process.name": "apache2", "system.process.started.time": " 99 days 18 hours 24 minutes 19 seconds", "system.process.started.time.seconds": 8619859, "system.process.threads": 27, "system.process.user": "www-data", "system.process.virtual.memory.bytes": 1241518080}]}, {"correlation.metrics": ["system.process.network.connection"], "system.process": [{"status": "Up", "system.process": "apache2|/usr/sbin/apache2 -k start", "system.process.command": "/usr/sbin/apache2 -k start", "system.process.cpu.percent": 0, "system.process.handles": 0, "system.process.id": 789, "system.process.memory.used.bytes": 2199552, "system.process.memory.used.percent": 0.2, "system.process.name": "apache2", "system.process.started.time": " 99 days 18 hours 29 minutes 19 seconds", "system.process.started.time.seconds": 8620159, "system.process.threads": 1, "system.process.user": "root", "system.process.virtual.memory.bytes": 6836224}, {"status": "Up", "system.process": "apache2|/usr/sbin/apache2 -k start", "system.process.command": "/usr/sbin/apache2 -k start", "system.process.cpu.percent": 0, "system.process.handles": 0, "system.process.id": 803, "system.process.memory.used.bytes": 4956160, "system.process.memory.used.percent": 0.4, "system.process.name": "apache2", "system.process.started.time": " 99 days 18 hours 29 minutes 18 seconds", "system.process.started.time.seconds": 8620158, "system.process.threads": 27, "system.process.user": "www-data", "system.process.virtual.memory.bytes": 1241468928}, {"status": "Up", "system.process": "apache2|/usr/sbin/apache2 -k start", "system.process.command": "/usr/sbin/apache2 -k start", "system.process.cpu.percent": 0, "system.process.handles": 0, "system.process.id": 804, "system.process.memory.used.bytes": 5206016, "system.process.memory.used.percent": 0.5, "system.process.name": "apache2", "system.process.started.time": " 99 days 18 hours 29 minutes 18 seconds", "system.process.started.time.seconds": 8620158, "system.process.threads": 27, "system.process.user": "www-data", "system.process.virtual.memory.bytes": 1241518080}]}, {"correlation.metrics": ["system.process.network.connection"], "system.process": [{"status": "Up", "system.process": "apache2|/usr/sbin/apache2 -k start", "system.process.command": "/usr/sbin/apache2 -k start", "system.process.cpu.percent": 0, "system.process.handles": 0, "system.process.id": 789, "system.process.memory.used.bytes": 2199552, "system.process.memory.used.percent": 0.2, "system.process.name": "apache2", "system.process.started.time": " 99 days 18 hours 34 minutes 21 seconds", "system.process.started.time.seconds": 8620461, "system.process.threads": 1, "system.process.user": "root", "system.process.virtual.memory.bytes": 6836224}, {"status": "Up", "system.process": "apache2|/usr/sbin/apache2 -k start", "system.process.command": "/usr/sbin/apache2 -k start", "system.process.cpu.percent": 0, "system.process.handles": 0, "system.process.id": 803, "system.process.memory.used.bytes": 4956160, "system.process.memory.used.percent": 0.4, "system.process.name": "apache2", "system.process.started.time": " 99 days 18 hours 34 minutes 20 seconds", "system.process.started.time.seconds": 8620460, "system.process.threads": 27, "system.process.user": "www-data", "system.process.virtual.memory.bytes": 1241468928}, {"status": "Up", "system.process": "apache2|/usr/sbin/apache2 -k start", "system.process.command": "/usr/sbin/apache2 -k start", "system.process.cpu.percent": 0, "system.process.handles": 0, "system.process.id": 804, "system.process.memory.used.bytes": 5206016, "system.process.memory.used.percent": 0.5, "system.process.name": "apache2", "system.process.started.time": " 99 days 18 hours 34 minutes 20 seconds", "system.process.started.time.seconds": 8620460, "system.process.threads": 27, "system.process.user": "www-data", "system.process.virtual.memory.bytes": 1241518080}]}, {"correlation.metrics": ["system.process.network.connection"], "system.process": [{"status": "Up", "system.process": "apache2|/usr/sbin/apache2 -k start", "system.process.command": "/usr/sbin/apache2 -k start", "system.process.cpu.percent": 0, "system.process.handles": 0, "system.process.id": 789, "system.process.memory.used.bytes": 2199552, "system.process.memory.used.percent": 0.2, "system.process.name": "apache2", "system.process.started.time": " 99 days 18 hours 44 minutes 20 seconds", "system.process.started.time.seconds": 8621060, "system.process.threads": 1, "system.process.user": "root", "system.process.virtual.memory.bytes": 6836224}, {"status": "Up", "system.process": "apache2|/usr/sbin/apache2 -k start", "system.process.command": "/usr/sbin/apache2 -k start", "system.process.cpu.percent": 0, "system.process.handles": 0, "system.process.id": 803, "system.process.memory.used.bytes": 4956160, "system.process.memory.used.percent": 0.4, "system.process.name": "apache2", "system.process.started.time": " 99 days 18 hours 44 minutes 19 seconds", "system.process.started.time.seconds": 8621059, "system.process.threads": 27, "system.process.user": "www-data", "system.process.virtual.memory.bytes": 1241468928}, {"status": "Up", "system.process": "apache2|/usr/sbin/apache2 -k start", "system.process.command": "/usr/sbin/apache2 -k start", "system.process.cpu.percent": 0, "system.process.handles": 0, "system.process.id": 804, "system.process.memory.used.bytes": 5206016, "system.process.memory.used.percent": 0.5, "system.process.name": "apache2", "system.process.started.time": " 99 days 18 hours 44 minutes 19 seconds", "system.process.started.time.seconds": 8621059, "system.process.threads": 27, "system.process.user": "www-data", "system.process.virtual.memory.bytes": 1241518080}]}], "rediscovery": null}, "172.16.8.167": {"discovery": null, "collect": [{"correlation.metrics": ["system.process.network.connection"], "system.process": [{"status": "Down", "system.process": "evolutio|/usr/libexec/evolution-calendar-factory-subprocess --factory all --bus-name org.gnome.evolution.dataserver.Subprocess.Backend.Calendarx2816x2 --own-path /org/gnome/evolution/dataserver/Subprocess/Backend/Calendar/2816/2", "system.process.command": "/usr/libexec/evolution-calendar-factory-subprocess --factory all --bus-name org.gnome.evolution.dataserver.Subprocess.Backend.Calendarx2816x2 --own-path /org/gnome/evolution/dataserver/Subprocess/Backend/Calendar/2816/2", "system.process.name": "evolutio"}, {"status": "Down", "system.process": "evolutio|/usr/libexec/evolution-addressbook-factory-subprocess --factory all --bus-name org.gnome.evolution.dataserver.Subprocess.Backend.AddressBookx2902x2 --own-path /org/gnome/evolution/dataserver/Subprocess/Backend/AddressBook/2902/2", "system.process.command": "/usr/libexec/evolution-addressbook-factory-subprocess --factory all --bus-name org.gnome.evolution.dataserver.Subprocess.Backend.AddressBookx2902x2 --own-path /org/gnome/evolution/dataserver/Subprocess/Backend/AddressBook/2902/2", "system.process.name": "evolutio"}]}, {"correlation.metrics": ["system.process.network.connection"], "system.process": [{"status": "Down", "system.process": "evolutio|/usr/libexec/evolution-calendar-factory-subprocess --factory all --bus-name org.gnome.evolution.dataserver.Subprocess.Backend.Calendarx2816x2 --own-path /org/gnome/evolution/dataserver/Subprocess/Backend/Calendar/2816/2", "system.process.command": "/usr/libexec/evolution-calendar-factory-subprocess --factory all --bus-name org.gnome.evolution.dataserver.Subprocess.Backend.Calendarx2816x2 --own-path /org/gnome/evolution/dataserver/Subprocess/Backend/Calendar/2816/2", "system.process.name": "evolutio"}, {"status": "Down", "system.process": "evolutio|/usr/libexec/evolution-addressbook-factory-subprocess --factory all --bus-name org.gnome.evolution.dataserver.Subprocess.Backend.AddressBookx2902x2 --own-path /org/gnome/evolution/dataserver/Subprocess/Backend/AddressBook/2902/2", "system.process.command": "/usr/libexec/evolution-addressbook-factory-subprocess --factory all --bus-name org.gnome.evolution.dataserver.Subprocess.Backend.AddressBookx2902x2 --own-path /org/gnome/evolution/dataserver/Subprocess/Backend/AddressBook/2902/2", "system.process.name": "evolutio"}]}, {"correlation.metrics": ["system.process.network.connection"], "system.process": [{"status": "Down", "system.process": "evolutio|/usr/libexec/evolution-calendar-factory-subprocess --factory all --bus-name org.gnome.evolution.dataserver.Subprocess.Backend.Calendarx2816x2 --own-path /org/gnome/evolution/dataserver/Subprocess/Backend/Calendar/2816/2", "system.process.command": "/usr/libexec/evolution-calendar-factory-subprocess --factory all --bus-name org.gnome.evolution.dataserver.Subprocess.Backend.Calendarx2816x2 --own-path /org/gnome/evolution/dataserver/Subprocess/Backend/Calendar/2816/2", "system.process.name": "evolutio"}, {"status": "Down", "system.process": "evolutio|/usr/libexec/evolution-addressbook-factory-subprocess --factory all --bus-name org.gnome.evolution.dataserver.Subprocess.Backend.AddressBookx2902x2 --own-path /org/gnome/evolution/dataserver/Subprocess/Backend/AddressBook/2902/2", "system.process.command": "/usr/libexec/evolution-addressbook-factory-subprocess --factory all --bus-name org.gnome.evolution.dataserver.Subprocess.Backend.AddressBookx2902x2 --own-path /org/gnome/evolution/dataserver/Subprocess/Backend/AddressBook/2902/2", "system.process.name": "evolutio"}]}, {"correlation.metrics": ["system.process.network.connection"], "system.process": [{"status": "Down", "system.process": "evolutio|/usr/libexec/evolution-calendar-factory-subprocess --factory all --bus-name org.gnome.evolution.dataserver.Subprocess.Backend.Calendarx2816x2 --own-path /org/gnome/evolution/dataserver/Subprocess/Backend/Calendar/2816/2", "system.process.command": "/usr/libexec/evolution-calendar-factory-subprocess --factory all --bus-name org.gnome.evolution.dataserver.Subprocess.Backend.Calendarx2816x2 --own-path /org/gnome/evolution/dataserver/Subprocess/Backend/Calendar/2816/2", "system.process.name": "evolutio"}, {"status": "Down", "system.process": "evolutio|/usr/libexec/evolution-addressbook-factory-subprocess --factory all --bus-name org.gnome.evolution.dataserver.Subprocess.Backend.AddressBookx2902x2 --own-path /org/gnome/evolution/dataserver/Subprocess/Backend/AddressBook/2902/2", "system.process.command": "/usr/libexec/evolution-addressbook-factory-subprocess --factory all --bus-name org.gnome.evolution.dataserver.Subprocess.Backend.AddressBookx2902x2 --own-path /org/gnome/evolution/dataserver/Subprocess/Backend/AddressBook/2902/2", "system.process.name": "evolutio"}]}, {"correlation.metrics": ["system.process.network.connection"], "system.process": [{"status": "Down", "system.process": "evolutio|/usr/libexec/evolution-calendar-factory-subprocess --factory all --bus-name org.gnome.evolution.dataserver.Subprocess.Backend.Calendarx2816x2 --own-path /org/gnome/evolution/dataserver/Subprocess/Backend/Calendar/2816/2", "system.process.command": "/usr/libexec/evolution-calendar-factory-subprocess --factory all --bus-name org.gnome.evolution.dataserver.Subprocess.Backend.Calendarx2816x2 --own-path /org/gnome/evolution/dataserver/Subprocess/Backend/Calendar/2816/2", "system.process.name": "evolutio"}, {"status": "Down", "system.process": "evolutio|/usr/libexec/evolution-addressbook-factory-subprocess --factory all --bus-name org.gnome.evolution.dataserver.Subprocess.Backend.AddressBookx2902x2 --own-path /org/gnome/evolution/dataserver/Subprocess/Backend/AddressBook/2902/2", "system.process.command": "/usr/libexec/evolution-addressbook-factory-subprocess --factory all --bus-name org.gnome.evolution.dataserver.Subprocess.Backend.AddressBookx2902x2 --own-path /org/gnome/evolution/dataserver/Subprocess/Backend/AddressBook/2902/2", "system.process.name": "evolutio"}]}, {"correlation.metrics": ["system.process.network.connection"], "system.process": [{"status": "Down", "system.process": "evolutio|/usr/libexec/evolution-calendar-factory-subprocess --factory all --bus-name org.gnome.evolution.dataserver.Subprocess.Backend.Calendarx2816x2 --own-path /org/gnome/evolution/dataserver/Subprocess/Backend/Calendar/2816/2", "system.process.command": "/usr/libexec/evolution-calendar-factory-subprocess --factory all --bus-name org.gnome.evolution.dataserver.Subprocess.Backend.Calendarx2816x2 --own-path /org/gnome/evolution/dataserver/Subprocess/Backend/Calendar/2816/2", "system.process.name": "evolutio"}, {"status": "Down", "system.process": "evolutio|/usr/libexec/evolution-addressbook-factory-subprocess --factory all --bus-name org.gnome.evolution.dataserver.Subprocess.Backend.AddressBookx2902x2 --own-path /org/gnome/evolution/dataserver/Subprocess/Backend/AddressBook/2902/2", "system.process.command": "/usr/libexec/evolution-addressbook-factory-subprocess --factory all --bus-name org.gnome.evolution.dataserver.Subprocess.Backend.AddressBookx2902x2 --own-path /org/gnome/evolution/dataserver/Subprocess/Backend/AddressBook/2902/2", "system.process.name": "evolutio"}]}, {"correlation.metrics": ["system.process.network.connection"], "system.process": [{"status": "Down", "system.process": "evolutio|/usr/libexec/evolution-calendar-factory-subprocess --factory all --bus-name org.gnome.evolution.dataserver.Subprocess.Backend.Calendarx2816x2 --own-path /org/gnome/evolution/dataserver/Subprocess/Backend/Calendar/2816/2", "system.process.command": "/usr/libexec/evolution-calendar-factory-subprocess --factory all --bus-name org.gnome.evolution.dataserver.Subprocess.Backend.Calendarx2816x2 --own-path /org/gnome/evolution/dataserver/Subprocess/Backend/Calendar/2816/2", "system.process.name": "evolutio"}, {"status": "Down", "system.process": "evolutio|/usr/libexec/evolution-addressbook-factory-subprocess --factory all --bus-name org.gnome.evolution.dataserver.Subprocess.Backend.AddressBookx2902x2 --own-path /org/gnome/evolution/dataserver/Subprocess/Backend/AddressBook/2902/2", "system.process.command": "/usr/libexec/evolution-addressbook-factory-subprocess --factory all --bus-name org.gnome.evolution.dataserver.Subprocess.Backend.AddressBookx2902x2 --own-path /org/gnome/evolution/dataserver/Subprocess/Backend/AddressBook/2902/2", "system.process.name": "evolutio"}]}, {"correlation.metrics": ["system.process.network.connection"], "system.process": [{"status": "Down", "system.process": "evolutio|/usr/libexec/evolution-calendar-factory-subprocess --factory all --bus-name org.gnome.evolution.dataserver.Subprocess.Backend.Calendarx2816x2 --own-path /org/gnome/evolution/dataserver/Subprocess/Backend/Calendar/2816/2", "system.process.command": "/usr/libexec/evolution-calendar-factory-subprocess --factory all --bus-name org.gnome.evolution.dataserver.Subprocess.Backend.Calendarx2816x2 --own-path /org/gnome/evolution/dataserver/Subprocess/Backend/Calendar/2816/2", "system.process.name": "evolutio"}, {"status": "Down", "system.process": "evolutio|/usr/libexec/evolution-addressbook-factory-subprocess --factory all --bus-name org.gnome.evolution.dataserver.Subprocess.Backend.AddressBookx2902x2 --own-path /org/gnome/evolution/dataserver/Subprocess/Backend/AddressBook/2902/2", "system.process.command": "/usr/libexec/evolution-addressbook-factory-subprocess --factory all --bus-name org.gnome.evolution.dataserver.Subprocess.Backend.AddressBookx2902x2 --own-path /org/gnome/evolution/dataserver/Subprocess/Backend/AddressBook/2902/2", "system.process.name": "evolutio"}]}], "rediscovery": null}, "************": {"discovery": null, "collect": [{"correlation.metrics": ["system.process.network.connection"], "system.process": [{"status": "Up", "system.process": "postgres|postgres: 16/main: logical replication launcher", "system.process.command": "postgres: 16/main: logical replication launcher", "system.process.cpu.percent": 0, "system.process.handles": 63, "system.process.id": 1674276, "system.process.memory.used.bytes": 6754304, "system.process.memory.used.percent": 0, "system.process.name": "postgres", "system.process.started.time": " 19 days 22 hours 49 minutes 21 seconds", "system.process.started.time.seconds": 1723761, "system.process.threads": 1, "system.process.user": "postgres", "system.process.virtual.memory.bytes": 230215680}, {"status": "Up", "system.process": "postgres|/usr/lib/postgresql/16/bin/postgres -D /var/lib/postgresql/16/main -c config_file=/etc/postgresql/16/main/postgresql.conf", "system.process.command": "/usr/lib/postgresql/16/bin/postgres -D /var/lib/postgresql/16/main -c config_file=/etc/postgresql/16/main/postgresql.conf", "system.process.cpu.percent": 0, "system.process.handles": 64, "system.process.id": 1674270, "system.process.memory.used.bytes": 20971520, "system.process.memory.used.percent": 0.1, "system.process.name": "postgres", "system.process.started.time": " 19 days 22 hours 49 minutes 21 seconds", "system.process.started.time.seconds": 1723761, "system.process.threads": 1, "system.process.user": "postgres", "system.process.virtual.memory.bytes": 228597760}, {"status": "Up", "system.process": "postgres|postgres: 16/main: checkpointer", "system.process.command": "postgres: 16/main: checkpointer", "system.process.cpu.percent": 0, "system.process.handles": 62, "system.process.id": 1674271, "system.process.memory.used.bytes": 120979456, "system.process.memory.used.percent": 0.9, "system.process.name": "postgres", "system.process.started.time": " 19 days 22 hours 49 minutes 21 seconds", "system.process.started.time.seconds": 1723761, "system.process.threads": 1, "system.process.user": "postgres", "system.process.virtual.memory.bytes": 229064704}, {"status": "Up", "system.process": "postgres|postgres: 16/main: background writer", "system.process.command": "postgres: 16/main: background writer", "system.process.cpu.percent": 0, "system.process.handles": 61, "system.process.id": 1674272, "system.process.memory.used.bytes": 8327168, "system.process.memory.used.percent": 0, "system.process.name": "postgres", "system.process.started.time": " 19 days 22 hours 49 minutes 21 seconds", "system.process.started.time.seconds": 1723761, "system.process.threads": 1, "system.process.user": "postgres", "system.process.virtual.memory.bytes": 228753408}, {"status": "Up", "system.process": "postgres|postgres: 16/main: walwriter", "system.process.command": "postgres: 16/main: walwriter", "system.process.cpu.percent": 0, "system.process.handles": 62, "system.process.id": 1674274, "system.process.memory.used.bytes": 9113600, "system.process.memory.used.percent": 0, "system.process.name": "postgres", "system.process.started.time": " 19 days 22 hours 49 minutes 21 seconds", "system.process.started.time.seconds": 1723761, "system.process.threads": 1, "system.process.user": "postgres", "system.process.virtual.memory.bytes": 228597760}, {"status": "Up", "system.process": "postgres|postgres: 16/main: autovacuum launcher", "system.process.command": "postgres: 16/main: autovacuum launcher", "system.process.cpu.percent": 0, "system.process.handles": 63, "system.process.id": 1674275, "system.process.memory.used.bytes": 9244672, "system.process.memory.used.percent": 0, "system.process.name": "postgres", "system.process.started.time": " 19 days 22 hours 49 minutes 21 seconds", "system.process.started.time.seconds": 1723761, "system.process.threads": 1, "system.process.user": "postgres", "system.process.virtual.memory.bytes": 230232064}, {"status": "Down", "system.process": "postgres|postgres: 16/main: motadata motadata 127.0.0.1(38154) idle", "system.process.command": "postgres: 16/main: motadata motadata 127.0.0.1(38154) idle", "system.process.name": "postgres"}, {"status": "Down", "system.process": "postgres|postgres: 16/main: motadata motadata 127.0.0.1(38166) idle", "system.process.command": "postgres: 16/main: motadata motadata 127.0.0.1(38166) idle", "system.process.name": "postgres"}, {"status": "Down", "system.process": "postgres|postgres: 16/main: motadata motadata 127.0.0.1(38170) idle", "system.process.command": "postgres: 16/main: motadata motadata 127.0.0.1(38170) idle", "system.process.name": "postgres"}, {"status": "Down", "system.process": "postgres|postgres: 16/main: motadata motadata 127.0.0.1(38178) idle", "system.process.command": "postgres: 16/main: motadata motadata 127.0.0.1(38178) idle", "system.process.name": "postgres"}], "system.process.network.connection": [{"system.process": "postgres|/usr/lib/postgresql/16/bin/postgres -D /var/lib/postgresql/16/main -c config_file=/etc/postgresql/16/main/postgresql.conf", "system.process.destination.ip": "************", "system.process.destination.port": "*", "system.process.source.ip": "************", "system.process.source.port": "5432"}]}, {"correlation.metrics": ["system.process.network.connection"], "system.process": [{"status": "Up", "system.process": "postgres|postgres: 16/main: background writer", "system.process.command": "postgres: 16/main: background writer", "system.process.cpu.percent": 0, "system.process.handles": 61, "system.process.id": 1674272, "system.process.memory.used.bytes": 8327168, "system.process.memory.used.percent": 0, "system.process.name": "postgres", "system.process.started.time": " 19 days 22 hours 51 minutes 23 seconds", "system.process.started.time.seconds": 1723883, "system.process.threads": 1, "system.process.user": "postgres", "system.process.virtual.memory.bytes": 228753408}, {"status": "Up", "system.process": "postgres|postgres: 16/main: walwriter", "system.process.command": "postgres: 16/main: walwriter", "system.process.cpu.percent": 0, "system.process.handles": 62, "system.process.id": 1674274, "system.process.memory.used.bytes": 9113600, "system.process.memory.used.percent": 0, "system.process.name": "postgres", "system.process.started.time": " 19 days 22 hours 51 minutes 23 seconds", "system.process.started.time.seconds": 1723883, "system.process.threads": 1, "system.process.user": "postgres", "system.process.virtual.memory.bytes": 228597760}, {"status": "Up", "system.process": "postgres|postgres: 16/main: autovacuum launcher", "system.process.command": "postgres: 16/main: autovacuum launcher", "system.process.cpu.percent": 0, "system.process.handles": 63, "system.process.id": 1674275, "system.process.memory.used.bytes": 9244672, "system.process.memory.used.percent": 0, "system.process.name": "postgres", "system.process.started.time": " 19 days 22 hours 51 minutes 23 seconds", "system.process.started.time.seconds": 1723883, "system.process.threads": 1, "system.process.user": "postgres", "system.process.virtual.memory.bytes": 230232064}, {"status": "Up", "system.process": "postgres|postgres: 16/main: logical replication launcher", "system.process.command": "postgres: 16/main: logical replication launcher", "system.process.cpu.percent": 0, "system.process.handles": 63, "system.process.id": 1674276, "system.process.memory.used.bytes": 6754304, "system.process.memory.used.percent": 0, "system.process.name": "postgres", "system.process.started.time": " 19 days 22 hours 51 minutes 23 seconds", "system.process.started.time.seconds": 1723883, "system.process.threads": 1, "system.process.user": "postgres", "system.process.virtual.memory.bytes": 230215680}, {"status": "Up", "system.process": "postgres|/usr/lib/postgresql/16/bin/postgres -D /var/lib/postgresql/16/main -c config_file=/etc/postgresql/16/main/postgresql.conf", "system.process.command": "/usr/lib/postgresql/16/bin/postgres -D /var/lib/postgresql/16/main -c config_file=/etc/postgresql/16/main/postgresql.conf", "system.process.cpu.percent": 0, "system.process.handles": 64, "system.process.id": 1674270, "system.process.memory.used.bytes": 20971520, "system.process.memory.used.percent": 0.1, "system.process.name": "postgres", "system.process.started.time": " 19 days 22 hours 51 minutes 23 seconds", "system.process.started.time.seconds": 1723883, "system.process.threads": 1, "system.process.user": "postgres", "system.process.virtual.memory.bytes": 228597760}, {"status": "Up", "system.process": "postgres|postgres: 16/main: checkpointer", "system.process.command": "postgres: 16/main: checkpointer", "system.process.cpu.percent": 0, "system.process.handles": 62, "system.process.id": 1674271, "system.process.memory.used.bytes": 120979456, "system.process.memory.used.percent": 0.9, "system.process.name": "postgres", "system.process.started.time": " 19 days 22 hours 51 minutes 23 seconds", "system.process.started.time.seconds": 1723883, "system.process.threads": 1, "system.process.user": "postgres", "system.process.virtual.memory.bytes": 229064704}, {"status": "Down", "system.process": "postgres|postgres: 16/main: motadata motadata 127.0.0.1(38154) idle", "system.process.command": "postgres: 16/main: motadata motadata 127.0.0.1(38154) idle", "system.process.name": "postgres"}, {"status": "Down", "system.process": "postgres|postgres: 16/main: motadata motadata 127.0.0.1(38166) idle", "system.process.command": "postgres: 16/main: motadata motadata 127.0.0.1(38166) idle", "system.process.name": "postgres"}, {"status": "Down", "system.process": "postgres|postgres: 16/main: motadata motadata 127.0.0.1(38170) idle", "system.process.command": "postgres: 16/main: motadata motadata 127.0.0.1(38170) idle", "system.process.name": "postgres"}, {"status": "Down", "system.process": "postgres|postgres: 16/main: motadata motadata 127.0.0.1(38178) idle", "system.process.command": "postgres: 16/main: motadata motadata 127.0.0.1(38178) idle", "system.process.name": "postgres"}], "system.process.network.connection": [{"system.process": "postgres|/usr/lib/postgresql/16/bin/postgres -D /var/lib/postgresql/16/main -c config_file=/etc/postgresql/16/main/postgresql.conf", "system.process.destination.ip": "************", "system.process.destination.port": "*", "system.process.source.ip": "************", "system.process.source.port": "5432"}]}, {"correlation.metrics": ["system.process.network.connection"], "system.process": [{"status": "Up", "system.process": "postgres|postgres: 16/main: autovacuum launcher", "system.process.command": "postgres: 16/main: autovacuum launcher", "system.process.cpu.percent": 0, "system.process.handles": 63, "system.process.id": 1674275, "system.process.memory.used.bytes": 9244672, "system.process.memory.used.percent": 0, "system.process.name": "postgres", "system.process.started.time": " 19 days 22 hours 56 minutes 24 seconds", "system.process.started.time.seconds": 1724184, "system.process.threads": 1, "system.process.user": "postgres", "system.process.virtual.memory.bytes": 230232064}, {"status": "Up", "system.process": "postgres|postgres: 16/main: logical replication launcher", "system.process.command": "postgres: 16/main: logical replication launcher", "system.process.cpu.percent": 0, "system.process.handles": 63, "system.process.id": 1674276, "system.process.memory.used.bytes": 6754304, "system.process.memory.used.percent": 0, "system.process.name": "postgres", "system.process.started.time": " 19 days 22 hours 56 minutes 24 seconds", "system.process.started.time.seconds": 1724184, "system.process.threads": 1, "system.process.user": "postgres", "system.process.virtual.memory.bytes": 230215680}, {"status": "Up", "system.process": "postgres|/usr/lib/postgresql/16/bin/postgres -D /var/lib/postgresql/16/main -c config_file=/etc/postgresql/16/main/postgresql.conf", "system.process.command": "/usr/lib/postgresql/16/bin/postgres -D /var/lib/postgresql/16/main -c config_file=/etc/postgresql/16/main/postgresql.conf", "system.process.cpu.percent": 0, "system.process.handles": 64, "system.process.id": 1674270, "system.process.memory.used.bytes": 20971520, "system.process.memory.used.percent": 0.1, "system.process.name": "postgres", "system.process.started.time": " 19 days 22 hours 56 minutes 24 seconds", "system.process.started.time.seconds": 1724184, "system.process.threads": 1, "system.process.user": "postgres", "system.process.virtual.memory.bytes": 228597760}, {"status": "Up", "system.process": "postgres|postgres: 16/main: checkpointer", "system.process.command": "postgres: 16/main: checkpointer", "system.process.cpu.percent": 0, "system.process.handles": 62, "system.process.id": 1674271, "system.process.memory.used.bytes": 120979456, "system.process.memory.used.percent": 0.9, "system.process.name": "postgres", "system.process.started.time": " 19 days 22 hours 56 minutes 24 seconds", "system.process.started.time.seconds": 1724184, "system.process.threads": 1, "system.process.user": "postgres", "system.process.virtual.memory.bytes": 229064704}, {"status": "Up", "system.process": "postgres|postgres: 16/main: background writer", "system.process.command": "postgres: 16/main: background writer", "system.process.cpu.percent": 0, "system.process.handles": 61, "system.process.id": 1674272, "system.process.memory.used.bytes": 8327168, "system.process.memory.used.percent": 0, "system.process.name": "postgres", "system.process.started.time": " 19 days 22 hours 56 minutes 24 seconds", "system.process.started.time.seconds": 1724184, "system.process.threads": 1, "system.process.user": "postgres", "system.process.virtual.memory.bytes": 228753408}, {"status": "Up", "system.process": "postgres|postgres: 16/main: walwriter", "system.process.command": "postgres: 16/main: walwriter", "system.process.cpu.percent": 0, "system.process.handles": 62, "system.process.id": 1674274, "system.process.memory.used.bytes": 9113600, "system.process.memory.used.percent": 0, "system.process.name": "postgres", "system.process.started.time": " 19 days 22 hours 56 minutes 24 seconds", "system.process.started.time.seconds": 1724184, "system.process.threads": 1, "system.process.user": "postgres", "system.process.virtual.memory.bytes": 228597760}, {"status": "Down", "system.process": "postgres|postgres: 16/main: motadata motadata 127.0.0.1(38154) idle", "system.process.command": "postgres: 16/main: motadata motadata 127.0.0.1(38154) idle", "system.process.name": "postgres"}, {"status": "Down", "system.process": "postgres|postgres: 16/main: motadata motadata 127.0.0.1(38166) idle", "system.process.command": "postgres: 16/main: motadata motadata 127.0.0.1(38166) idle", "system.process.name": "postgres"}, {"status": "Down", "system.process": "postgres|postgres: 16/main: motadata motadata 127.0.0.1(38170) idle", "system.process.command": "postgres: 16/main: motadata motadata 127.0.0.1(38170) idle", "system.process.name": "postgres"}, {"status": "Down", "system.process": "postgres|postgres: 16/main: motadata motadata 127.0.0.1(38178) idle", "system.process.command": "postgres: 16/main: motadata motadata 127.0.0.1(38178) idle", "system.process.name": "postgres"}], "system.process.network.connection": [{"system.process": "postgres|/usr/lib/postgresql/16/bin/postgres -D /var/lib/postgresql/16/main -c config_file=/etc/postgresql/16/main/postgresql.conf", "system.process.destination.ip": "************", "system.process.destination.port": "*", "system.process.source.ip": "************", "system.process.source.port": "5432"}]}, {"correlation.metrics": ["system.process.network.connection"], "system.process": [{"status": "Up", "system.process": "postgres|postgres: 16/main: background writer", "system.process.command": "postgres: 16/main: background writer", "system.process.cpu.percent": 0, "system.process.handles": 61, "system.process.id": 1674272, "system.process.memory.used.bytes": 8327168, "system.process.memory.used.percent": 0, "system.process.name": "postgres", "system.process.started.time": " 19 days 23 hours 1 minutes 24 seconds", "system.process.started.time.seconds": 1724484, "system.process.threads": 1, "system.process.user": "postgres", "system.process.virtual.memory.bytes": 228753408}, {"status": "Up", "system.process": "postgres|postgres: 16/main: walwriter", "system.process.command": "postgres: 16/main: walwriter", "system.process.cpu.percent": 0, "system.process.handles": 62, "system.process.id": 1674274, "system.process.memory.used.bytes": 9113600, "system.process.memory.used.percent": 0, "system.process.name": "postgres", "system.process.started.time": " 19 days 23 hours 1 minutes 24 seconds", "system.process.started.time.seconds": 1724484, "system.process.threads": 1, "system.process.user": "postgres", "system.process.virtual.memory.bytes": 228597760}, {"status": "Up", "system.process": "postgres|postgres: 16/main: autovacuum launcher", "system.process.command": "postgres: 16/main: autovacuum launcher", "system.process.cpu.percent": 0, "system.process.handles": 63, "system.process.id": 1674275, "system.process.memory.used.bytes": 9244672, "system.process.memory.used.percent": 0, "system.process.name": "postgres", "system.process.started.time": " 19 days 23 hours 1 minutes 24 seconds", "system.process.started.time.seconds": 1724484, "system.process.threads": 1, "system.process.user": "postgres", "system.process.virtual.memory.bytes": 230232064}, {"status": "Up", "system.process": "postgres|postgres: 16/main: logical replication launcher", "system.process.command": "postgres: 16/main: logical replication launcher", "system.process.cpu.percent": 0, "system.process.handles": 63, "system.process.id": 1674276, "system.process.memory.used.bytes": 6754304, "system.process.memory.used.percent": 0, "system.process.name": "postgres", "system.process.started.time": " 19 days 23 hours 1 minutes 24 seconds", "system.process.started.time.seconds": 1724484, "system.process.threads": 1, "system.process.user": "postgres", "system.process.virtual.memory.bytes": 230215680}, {"status": "Up", "system.process": "postgres|/usr/lib/postgresql/16/bin/postgres -D /var/lib/postgresql/16/main -c config_file=/etc/postgresql/16/main/postgresql.conf", "system.process.command": "/usr/lib/postgresql/16/bin/postgres -D /var/lib/postgresql/16/main -c config_file=/etc/postgresql/16/main/postgresql.conf", "system.process.cpu.percent": 0, "system.process.handles": 64, "system.process.id": 1674270, "system.process.memory.used.bytes": 20971520, "system.process.memory.used.percent": 0.1, "system.process.name": "postgres", "system.process.started.time": " 19 days 23 hours 1 minutes 24 seconds", "system.process.started.time.seconds": 1724484, "system.process.threads": 1, "system.process.user": "postgres", "system.process.virtual.memory.bytes": 228597760}, {"status": "Up", "system.process": "postgres|postgres: 16/main: checkpointer", "system.process.command": "postgres: 16/main: checkpointer", "system.process.cpu.percent": 0, "system.process.handles": 62, "system.process.id": 1674271, "system.process.memory.used.bytes": 120979456, "system.process.memory.used.percent": 0.9, "system.process.name": "postgres", "system.process.started.time": " 19 days 23 hours 1 minutes 24 seconds", "system.process.started.time.seconds": 1724484, "system.process.threads": 1, "system.process.user": "postgres", "system.process.virtual.memory.bytes": 229064704}, {"status": "Down", "system.process": "postgres|postgres: 16/main: motadata motadata 127.0.0.1(38154) idle", "system.process.command": "postgres: 16/main: motadata motadata 127.0.0.1(38154) idle", "system.process.name": "postgres"}, {"status": "Down", "system.process": "postgres|postgres: 16/main: motadata motadata 127.0.0.1(38166) idle", "system.process.command": "postgres: 16/main: motadata motadata 127.0.0.1(38166) idle", "system.process.name": "postgres"}, {"status": "Down", "system.process": "postgres|postgres: 16/main: motadata motadata 127.0.0.1(38170) idle", "system.process.command": "postgres: 16/main: motadata motadata 127.0.0.1(38170) idle", "system.process.name": "postgres"}, {"status": "Down", "system.process": "postgres|postgres: 16/main: motadata motadata 127.0.0.1(38178) idle", "system.process.command": "postgres: 16/main: motadata motadata 127.0.0.1(38178) idle", "system.process.name": "postgres"}], "system.process.network.connection": [{"system.process": "postgres|/usr/lib/postgresql/16/bin/postgres -D /var/lib/postgresql/16/main -c config_file=/etc/postgresql/16/main/postgresql.conf", "system.process.destination.ip": "************", "system.process.destination.port": "*", "system.process.source.ip": "************", "system.process.source.port": "5432"}]}, {"correlation.metrics": ["system.process.network.connection"], "system.process": [{"status": "Up", "system.process": "postgres|/usr/lib/postgresql/16/bin/postgres -D /var/lib/postgresql/16/main -c config_file=/etc/postgresql/16/main/postgresql.conf", "system.process.command": "/usr/lib/postgresql/16/bin/postgres -D /var/lib/postgresql/16/main -c config_file=/etc/postgresql/16/main/postgresql.conf", "system.process.cpu.percent": 0, "system.process.handles": 64, "system.process.id": 1674270, "system.process.memory.used.bytes": 20971520, "system.process.memory.used.percent": 0.1, "system.process.name": "postgres", "system.process.started.time": " 19 days 23 hours 6 minutes 23 seconds", "system.process.started.time.seconds": 1724783, "system.process.threads": 1, "system.process.user": "postgres", "system.process.virtual.memory.bytes": 228597760}, {"status": "Up", "system.process": "postgres|postgres: 16/main: checkpointer", "system.process.command": "postgres: 16/main: checkpointer", "system.process.cpu.percent": 0, "system.process.handles": 62, "system.process.id": 1674271, "system.process.memory.used.bytes": 120979456, "system.process.memory.used.percent": 0.9, "system.process.name": "postgres", "system.process.started.time": " 19 days 23 hours 6 minutes 23 seconds", "system.process.started.time.seconds": 1724783, "system.process.threads": 1, "system.process.user": "postgres", "system.process.virtual.memory.bytes": 229064704}, {"status": "Up", "system.process": "postgres|postgres: 16/main: background writer", "system.process.command": "postgres: 16/main: background writer", "system.process.cpu.percent": 0, "system.process.handles": 61, "system.process.id": 1674272, "system.process.memory.used.bytes": 8327168, "system.process.memory.used.percent": 0, "system.process.name": "postgres", "system.process.started.time": " 19 days 23 hours 6 minutes 23 seconds", "system.process.started.time.seconds": 1724783, "system.process.threads": 1, "system.process.user": "postgres", "system.process.virtual.memory.bytes": 228753408}, {"status": "Up", "system.process": "postgres|postgres: 16/main: walwriter", "system.process.command": "postgres: 16/main: walwriter", "system.process.cpu.percent": 0, "system.process.handles": 62, "system.process.id": 1674274, "system.process.memory.used.bytes": 9113600, "system.process.memory.used.percent": 0, "system.process.name": "postgres", "system.process.started.time": " 19 days 23 hours 6 minutes 23 seconds", "system.process.started.time.seconds": 1724783, "system.process.threads": 1, "system.process.user": "postgres", "system.process.virtual.memory.bytes": 228597760}, {"status": "Up", "system.process": "postgres|postgres: 16/main: autovacuum launcher", "system.process.command": "postgres: 16/main: autovacuum launcher", "system.process.cpu.percent": 0, "system.process.handles": 63, "system.process.id": 1674275, "system.process.memory.used.bytes": 9244672, "system.process.memory.used.percent": 0, "system.process.name": "postgres", "system.process.started.time": " 19 days 23 hours 6 minutes 23 seconds", "system.process.started.time.seconds": 1724783, "system.process.threads": 1, "system.process.user": "postgres", "system.process.virtual.memory.bytes": 230232064}, {"status": "Up", "system.process": "postgres|postgres: 16/main: logical replication launcher", "system.process.command": "postgres: 16/main: logical replication launcher", "system.process.cpu.percent": 0, "system.process.handles": 63, "system.process.id": 1674276, "system.process.memory.used.bytes": 6754304, "system.process.memory.used.percent": 0, "system.process.name": "postgres", "system.process.started.time": " 19 days 23 hours 6 minutes 23 seconds", "system.process.started.time.seconds": 1724783, "system.process.threads": 1, "system.process.user": "postgres", "system.process.virtual.memory.bytes": 230215680}, {"status": "Down", "system.process": "postgres|postgres: 16/main: motadata motadata 127.0.0.1(38154) idle", "system.process.command": "postgres: 16/main: motadata motadata 127.0.0.1(38154) idle", "system.process.name": "postgres"}, {"status": "Down", "system.process": "postgres|postgres: 16/main: motadata motadata 127.0.0.1(38166) idle", "system.process.command": "postgres: 16/main: motadata motadata 127.0.0.1(38166) idle", "system.process.name": "postgres"}, {"status": "Down", "system.process": "postgres|postgres: 16/main: motadata motadata 127.0.0.1(38170) idle", "system.process.command": "postgres: 16/main: motadata motadata 127.0.0.1(38170) idle", "system.process.name": "postgres"}, {"status": "Down", "system.process": "postgres|postgres: 16/main: motadata motadata 127.0.0.1(38178) idle", "system.process.command": "postgres: 16/main: motadata motadata 127.0.0.1(38178) idle", "system.process.name": "postgres"}], "system.process.network.connection": [{"system.process": "postgres|/usr/lib/postgresql/16/bin/postgres -D /var/lib/postgresql/16/main -c config_file=/etc/postgresql/16/main/postgresql.conf", "system.process.destination.ip": "************", "system.process.destination.port": "*", "system.process.source.ip": "************", "system.process.source.port": "5432"}]}, {"correlation.metrics": ["system.process.network.connection"], "system.process": [{"status": "Up", "system.process": "postgres|postgres: 16/main: checkpointer", "system.process.command": "postgres: 16/main: checkpointer", "system.process.cpu.percent": 0, "system.process.handles": 62, "system.process.id": 1674271, "system.process.memory.used.bytes": 120979456, "system.process.memory.used.percent": 0.9, "system.process.name": "postgres", "system.process.started.time": " 19 days 23 hours 11 minutes 23 seconds", "system.process.started.time.seconds": 1725083, "system.process.threads": 1, "system.process.user": "postgres", "system.process.virtual.memory.bytes": 229064704}, {"status": "Up", "system.process": "postgres|postgres: 16/main: background writer", "system.process.command": "postgres: 16/main: background writer", "system.process.cpu.percent": 0, "system.process.handles": 61, "system.process.id": 1674272, "system.process.memory.used.bytes": 8327168, "system.process.memory.used.percent": 0, "system.process.name": "postgres", "system.process.started.time": " 19 days 23 hours 11 minutes 23 seconds", "system.process.started.time.seconds": 1725083, "system.process.threads": 1, "system.process.user": "postgres", "system.process.virtual.memory.bytes": 228753408}, {"status": "Up", "system.process": "postgres|postgres: 16/main: walwriter", "system.process.command": "postgres: 16/main: walwriter", "system.process.cpu.percent": 0, "system.process.handles": 62, "system.process.id": 1674274, "system.process.memory.used.bytes": 9113600, "system.process.memory.used.percent": 0, "system.process.name": "postgres", "system.process.started.time": " 19 days 23 hours 11 minutes 23 seconds", "system.process.started.time.seconds": 1725083, "system.process.threads": 1, "system.process.user": "postgres", "system.process.virtual.memory.bytes": 228597760}, {"status": "Up", "system.process": "postgres|postgres: 16/main: autovacuum launcher", "system.process.command": "postgres: 16/main: autovacuum launcher", "system.process.cpu.percent": 0, "system.process.handles": 63, "system.process.id": 1674275, "system.process.memory.used.bytes": 9244672, "system.process.memory.used.percent": 0, "system.process.name": "postgres", "system.process.started.time": " 19 days 23 hours 11 minutes 23 seconds", "system.process.started.time.seconds": 1725083, "system.process.threads": 1, "system.process.user": "postgres", "system.process.virtual.memory.bytes": 230232064}, {"status": "Up", "system.process": "postgres|postgres: 16/main: logical replication launcher", "system.process.command": "postgres: 16/main: logical replication launcher", "system.process.cpu.percent": 0, "system.process.handles": 63, "system.process.id": 1674276, "system.process.memory.used.bytes": 6754304, "system.process.memory.used.percent": 0, "system.process.name": "postgres", "system.process.started.time": " 19 days 23 hours 11 minutes 23 seconds", "system.process.started.time.seconds": 1725083, "system.process.threads": 1, "system.process.user": "postgres", "system.process.virtual.memory.bytes": 230215680}, {"status": "Up", "system.process": "postgres|/usr/lib/postgresql/16/bin/postgres -D /var/lib/postgresql/16/main -c config_file=/etc/postgresql/16/main/postgresql.conf", "system.process.command": "/usr/lib/postgresql/16/bin/postgres -D /var/lib/postgresql/16/main -c config_file=/etc/postgresql/16/main/postgresql.conf", "system.process.cpu.percent": 0, "system.process.handles": 64, "system.process.id": 1674270, "system.process.memory.used.bytes": 20971520, "system.process.memory.used.percent": 0.1, "system.process.name": "postgres", "system.process.started.time": " 19 days 23 hours 11 minutes 24 seconds", "system.process.started.time.seconds": 1725084, "system.process.threads": 1, "system.process.user": "postgres", "system.process.virtual.memory.bytes": 228597760}, {"status": "Down", "system.process": "postgres|postgres: 16/main: motadata motadata 127.0.0.1(38154) idle", "system.process.command": "postgres: 16/main: motadata motadata 127.0.0.1(38154) idle", "system.process.name": "postgres"}, {"status": "Down", "system.process": "postgres|postgres: 16/main: motadata motadata 127.0.0.1(38166) idle", "system.process.command": "postgres: 16/main: motadata motadata 127.0.0.1(38166) idle", "system.process.name": "postgres"}, {"status": "Down", "system.process": "postgres|postgres: 16/main: motadata motadata 127.0.0.1(38170) idle", "system.process.command": "postgres: 16/main: motadata motadata 127.0.0.1(38170) idle", "system.process.name": "postgres"}, {"status": "Down", "system.process": "postgres|postgres: 16/main: motadata motadata 127.0.0.1(38178) idle", "system.process.command": "postgres: 16/main: motadata motadata 127.0.0.1(38178) idle", "system.process.name": "postgres"}], "system.process.network.connection": [{"system.process": "postgres|/usr/lib/postgresql/16/bin/postgres -D /var/lib/postgresql/16/main -c config_file=/etc/postgresql/16/main/postgresql.conf", "system.process.destination.ip": "************", "system.process.destination.port": "*", "system.process.source.ip": "************", "system.process.source.port": "5432"}]}, {"correlation.metrics": ["system.process.network.connection"], "system.process": [{"status": "Up", "system.process": "postgres|/usr/lib/postgresql/16/bin/postgres -D /var/lib/postgresql/16/main -c config_file=/etc/postgresql/16/main/postgresql.conf", "system.process.command": "/usr/lib/postgresql/16/bin/postgres -D /var/lib/postgresql/16/main -c config_file=/etc/postgresql/16/main/postgresql.conf", "system.process.cpu.percent": 0, "system.process.handles": 64, "system.process.id": 1674270, "system.process.memory.used.bytes": 20971520, "system.process.memory.used.percent": 0.1, "system.process.name": "postgres", "system.process.started.time": " 19 days 23 hours 16 minutes 24 seconds", "system.process.started.time.seconds": 1725384, "system.process.threads": 1, "system.process.user": "postgres", "system.process.virtual.memory.bytes": 228597760}, {"status": "Up", "system.process": "postgres|postgres: 16/main: checkpointer", "system.process.command": "postgres: 16/main: checkpointer", "system.process.cpu.percent": 0, "system.process.handles": 62, "system.process.id": 1674271, "system.process.memory.used.bytes": 120979456, "system.process.memory.used.percent": 0.9, "system.process.name": "postgres", "system.process.started.time": " 19 days 23 hours 16 minutes 24 seconds", "system.process.started.time.seconds": 1725384, "system.process.threads": 1, "system.process.user": "postgres", "system.process.virtual.memory.bytes": 229064704}, {"status": "Up", "system.process": "postgres|postgres: 16/main: background writer", "system.process.command": "postgres: 16/main: background writer", "system.process.cpu.percent": 0, "system.process.handles": 61, "system.process.id": 1674272, "system.process.memory.used.bytes": 8327168, "system.process.memory.used.percent": 0, "system.process.name": "postgres", "system.process.started.time": " 19 days 23 hours 16 minutes 24 seconds", "system.process.started.time.seconds": 1725384, "system.process.threads": 1, "system.process.user": "postgres", "system.process.virtual.memory.bytes": 228753408}, {"status": "Up", "system.process": "postgres|postgres: 16/main: walwriter", "system.process.command": "postgres: 16/main: walwriter", "system.process.cpu.percent": 0, "system.process.handles": 62, "system.process.id": 1674274, "system.process.memory.used.bytes": 9113600, "system.process.memory.used.percent": 0, "system.process.name": "postgres", "system.process.started.time": " 19 days 23 hours 16 minutes 24 seconds", "system.process.started.time.seconds": 1725384, "system.process.threads": 1, "system.process.user": "postgres", "system.process.virtual.memory.bytes": 228597760}, {"status": "Up", "system.process": "postgres|postgres: 16/main: autovacuum launcher", "system.process.command": "postgres: 16/main: autovacuum launcher", "system.process.cpu.percent": 0, "system.process.handles": 63, "system.process.id": 1674275, "system.process.memory.used.bytes": 9244672, "system.process.memory.used.percent": 0, "system.process.name": "postgres", "system.process.started.time": " 19 days 23 hours 16 minutes 24 seconds", "system.process.started.time.seconds": 1725384, "system.process.threads": 1, "system.process.user": "postgres", "system.process.virtual.memory.bytes": 230232064}, {"status": "Up", "system.process": "postgres|postgres: 16/main: logical replication launcher", "system.process.command": "postgres: 16/main: logical replication launcher", "system.process.cpu.percent": 0, "system.process.handles": 63, "system.process.id": 1674276, "system.process.memory.used.bytes": 6754304, "system.process.memory.used.percent": 0, "system.process.name": "postgres", "system.process.started.time": " 19 days 23 hours 16 minutes 24 seconds", "system.process.started.time.seconds": 1725384, "system.process.threads": 1, "system.process.user": "postgres", "system.process.virtual.memory.bytes": 230215680}, {"status": "Down", "system.process": "postgres|postgres: 16/main: motadata motadata 127.0.0.1(38154) idle", "system.process.command": "postgres: 16/main: motadata motadata 127.0.0.1(38154) idle", "system.process.name": "postgres"}, {"status": "Down", "system.process": "postgres|postgres: 16/main: motadata motadata 127.0.0.1(38166) idle", "system.process.command": "postgres: 16/main: motadata motadata 127.0.0.1(38166) idle", "system.process.name": "postgres"}, {"status": "Down", "system.process": "postgres|postgres: 16/main: motadata motadata 127.0.0.1(38170) idle", "system.process.command": "postgres: 16/main: motadata motadata 127.0.0.1(38170) idle", "system.process.name": "postgres"}, {"status": "Down", "system.process": "postgres|postgres: 16/main: motadata motadata 127.0.0.1(38178) idle", "system.process.command": "postgres: 16/main: motadata motadata 127.0.0.1(38178) idle", "system.process.name": "postgres"}], "system.process.network.connection": [{"system.process": "postgres|/usr/lib/postgresql/16/bin/postgres -D /var/lib/postgresql/16/main -c config_file=/etc/postgresql/16/main/postgresql.conf", "system.process.destination.ip": "************", "system.process.destination.port": "*", "system.process.source.ip": "************", "system.process.source.port": "5432"}]}, {"correlation.metrics": ["system.process.network.connection"], "system.process": [{"status": "Up", "system.process": "postgres|postgres: 16/main: checkpointer", "system.process.command": "postgres: 16/main: checkpointer", "system.process.cpu.percent": 0, "system.process.handles": 62, "system.process.id": 1674271, "system.process.memory.used.bytes": 120979456, "system.process.memory.used.percent": 0.9, "system.process.name": "postgres", "system.process.started.time": " 19 days 23 hours 26 minutes 25 seconds", "system.process.started.time.seconds": 1725985, "system.process.threads": 1, "system.process.user": "postgres", "system.process.virtual.memory.bytes": 229064704}, {"status": "Up", "system.process": "postgres|postgres: 16/main: background writer", "system.process.command": "postgres: 16/main: background writer", "system.process.cpu.percent": 0, "system.process.handles": 61, "system.process.id": 1674272, "system.process.memory.used.bytes": 8327168, "system.process.memory.used.percent": 0, "system.process.name": "postgres", "system.process.started.time": " 19 days 23 hours 26 minutes 25 seconds", "system.process.started.time.seconds": 1725985, "system.process.threads": 1, "system.process.user": "postgres", "system.process.virtual.memory.bytes": 228753408}, {"status": "Up", "system.process": "postgres|postgres: 16/main: walwriter", "system.process.command": "postgres: 16/main: walwriter", "system.process.cpu.percent": 0, "system.process.handles": 62, "system.process.id": 1674274, "system.process.memory.used.bytes": 9113600, "system.process.memory.used.percent": 0, "system.process.name": "postgres", "system.process.started.time": " 19 days 23 hours 26 minutes 25 seconds", "system.process.started.time.seconds": 1725985, "system.process.threads": 1, "system.process.user": "postgres", "system.process.virtual.memory.bytes": 228597760}, {"status": "Up", "system.process": "postgres|postgres: 16/main: autovacuum launcher", "system.process.command": "postgres: 16/main: autovacuum launcher", "system.process.cpu.percent": 0, "system.process.handles": 63, "system.process.id": 1674275, "system.process.memory.used.bytes": 9244672, "system.process.memory.used.percent": 0, "system.process.name": "postgres", "system.process.started.time": " 19 days 23 hours 26 minutes 25 seconds", "system.process.started.time.seconds": 1725985, "system.process.threads": 1, "system.process.user": "postgres", "system.process.virtual.memory.bytes": 230232064}, {"status": "Up", "system.process": "postgres|postgres: 16/main: logical replication launcher", "system.process.command": "postgres: 16/main: logical replication launcher", "system.process.cpu.percent": 0, "system.process.handles": 63, "system.process.id": 1674276, "system.process.memory.used.bytes": 6754304, "system.process.memory.used.percent": 0, "system.process.name": "postgres", "system.process.started.time": " 19 days 23 hours 26 minutes 25 seconds", "system.process.started.time.seconds": 1725985, "system.process.threads": 1, "system.process.user": "postgres", "system.process.virtual.memory.bytes": 230215680}, {"status": "Up", "system.process": "postgres|/usr/lib/postgresql/16/bin/postgres -D /var/lib/postgresql/16/main -c config_file=/etc/postgresql/16/main/postgresql.conf", "system.process.command": "/usr/lib/postgresql/16/bin/postgres -D /var/lib/postgresql/16/main -c config_file=/etc/postgresql/16/main/postgresql.conf", "system.process.cpu.percent": 0, "system.process.handles": 64, "system.process.id": 1674270, "system.process.memory.used.bytes": 20971520, "system.process.memory.used.percent": 0.1, "system.process.name": "postgres", "system.process.started.time": " 19 days 23 hours 26 minutes 25 seconds", "system.process.started.time.seconds": 1725985, "system.process.threads": 1, "system.process.user": "postgres", "system.process.virtual.memory.bytes": 228597760}, {"status": "Down", "system.process": "postgres|postgres: 16/main: motadata motadata 127.0.0.1(38154) idle", "system.process.command": "postgres: 16/main: motadata motadata 127.0.0.1(38154) idle", "system.process.name": "postgres"}, {"status": "Down", "system.process": "postgres|postgres: 16/main: motadata motadata 127.0.0.1(38166) idle", "system.process.command": "postgres: 16/main: motadata motadata 127.0.0.1(38166) idle", "system.process.name": "postgres"}, {"status": "Down", "system.process": "postgres|postgres: 16/main: motadata motadata 127.0.0.1(38170) idle", "system.process.command": "postgres: 16/main: motadata motadata 127.0.0.1(38170) idle", "system.process.name": "postgres"}, {"status": "Down", "system.process": "postgres|postgres: 16/main: motadata motadata 127.0.0.1(38178) idle", "system.process.command": "postgres: 16/main: motadata motadata 127.0.0.1(38178) idle", "system.process.name": "postgres"}], "system.process.network.connection": [{"system.process": "postgres|/usr/lib/postgresql/16/bin/postgres -D /var/lib/postgresql/16/main -c config_file=/etc/postgresql/16/main/postgresql.conf", "system.process.destination.ip": "************", "system.process.destination.port": "*", "system.process.source.ip": "************", "system.process.source.port": "5432"}]}], "rediscovery": null}, "************": {"discovery": null, "collect": [{"correlation.metrics": ["system.process.network.connection"], "system.process": [{"status": "Up", "system.process": "beam.smp|/usr/lib64/erlang/erts-5.10.4/bin/beam.smp -W w -A 64 -P 1048576 -t 5000000 -stbt db -zdbbl 32000 -K true -- -root /usr/lib64/erlang -progname erl -- -home /var/lib/rabbitmq -- -pa /usr/lib/rabbitmq/lib/rabbitmq_server-3.6.9/ebin -noshell -noinput -s rabbit boot -sname rabbit@rhel-7 -boot start_sasl -kernel inet_default_connect_options [{nodelay,true}] -sasl errlog_type error -sasl sasl_error_logger false -rabbit error_logger {file,\"/var/log/rabbitmq/<EMAIL>\"} -rabbit sasl_error_logger {file,\"/var/log/rabbitmq/<EMAIL>\"} -rabbit enabled_plugins_file \"/etc/rabbitmq/enabled_plugins\" -rabbit plugins_dir \"/usr/lib/rabbitmq/plugins:/usr/lib/rabbitmq/lib/rabbitmq_server-3.6.9/plugins\" -rabbit plugins_expand_dir \"/var/lib/rabbitmq/mnesia/rabbit@rhel-7-plugins-expand\" -os_mon start_cpu_sup false -os_mon start_disksup false -os_mon start_memsup false -mnesia dir \"/var/lib/rabbitmq/mnesia/rabbit@rhel-7\" -kernel inet_dist_listen_min 25672 -kernel inet_dist_listen_max 25672", "system.process.command": "/usr/lib64/erlang/erts-5.10.4/bin/beam.smp -W w -A 64 -P 1048576 -t 5000000 -stbt db -zdbbl 32000 -K true -- -root /usr/lib64/erlang -progname erl -- -home /var/lib/rabbitmq -- -pa /usr/lib/rabbitmq/lib/rabbitmq_server-3.6.9/ebin -noshell -noinput -s rabbit boot -sname rabbit@rhel-7 -boot start_sasl -kernel inet_default_connect_options [{nodelay,true}] -sasl errlog_type error -sasl sasl_error_logger false -rabbit error_logger {file,\"/var/log/rabbitmq/<EMAIL>\"} -rabbit sasl_error_logger {file,\"/var/log/rabbitmq/<EMAIL>\"} -rabbit enabled_plugins_file \"/etc/rabbitmq/enabled_plugins\" -rabbit plugins_dir \"/usr/lib/rabbitmq/plugins:/usr/lib/rabbitmq/lib/rabbitmq_server-3.6.9/plugins\" -rabbit plugins_expand_dir \"/var/lib/rabbitmq/mnesia/rabbit@rhel-7-plugins-expand\" -os_mon start_cpu_sup false -os_mon start_disksup false -os_mon start_memsup false -mnesia dir \"/var/lib/rabbitmq/mnesia/rabbit@rhel-7\" -kernel inet_dist_listen_min 25672 -kernel inet_dist_listen_max 25672", "system.process.cpu.percent": 1.125, "system.process.id": 908, "system.process.memory.used.bytes": 122294272, "system.process.memory.used.percent": 6.3, "system.process.name": "beam.smp", "system.process.started.time": " 141 days 22 hours 27 minutes 3 seconds", "system.process.started.time.seconds": 12263223, "system.process.threads": 73, "system.process.user": "rabbitmq", "system.process.virtual.memory.bytes": 2679107584}, {"status": "Up", "system.process": "named|/usr/sbin/named -u named -c /etc/named.conf", "system.process.command": "/usr/sbin/named -u named -c /etc/named.conf", "system.process.cpu.percent": 0, "system.process.id": 1227, "system.process.memory.used.bytes": 104968192, "system.process.memory.used.percent": 5.4, "system.process.name": "named", "system.process.started.time": " 141 days 22 hours 27 minutes 2 seconds", "system.process.started.time.seconds": 12263222, "system.process.threads": 7, "system.process.user": "named", "system.process.virtual.memory.bytes": 399429632}], "system.process.network.connection": [{"system.process": "beam.smp|/usr/lib64/erlang/erts-5.10.4/bin/beam.smp -W w -A 64 -P 1048576 -t 5000000 -stbt db -zdbbl 32000 -K true -- -root /usr/lib64/erlang -progname erl -- -home /var/lib/rabbitmq -- -pa /usr/lib/rabbitmq/lib/rabbitmq_server-3.6.9/ebin -noshell -noinput -s rabbit boot -sname rabbit@rhel-7 -boot start_sasl -kernel inet_default_connect_options [{nodelay,true}] -sasl errlog_type error -sasl sasl_error_logger false -rabbit error_logger {file,\"/var/log/rabbitmq/<EMAIL>\"} -rabbit sasl_error_logger {file,\"/var/log/rabbitmq/<EMAIL>\"} -rabbit enabled_plugins_file \"/etc/rabbitmq/enabled_plugins\" -rabbit plugins_dir \"/usr/lib/rabbitmq/plugins:/usr/lib/rabbitmq/lib/rabbitmq_server-3.6.9/plugins\" -rabbit plugins_expand_dir \"/var/lib/rabbitmq/mnesia/rabbit@rhel-7-plugins-expand\" -os_mon start_cpu_sup false -os_mon start_disksup false -os_mon start_memsup false -mnesia dir \"/var/lib/rabbitmq/mnesia/rabbit@rhel-7\" -kernel inet_dist_listen_min 25672 -kernel inet_dist_listen_max 25672", "system.process.destination.ip": "************", "system.process.destination.port": "*", "system.process.source.ip": "************", "system.process.source.port": "25672"}]}, {"correlation.metrics": ["system.process.network.connection"], "system.process": [{"status": "Up", "system.process": "beam.smp|/usr/lib64/erlang/erts-5.10.4/bin/beam.smp -W w -A 64 -P 1048576 -t 5000000 -stbt db -zdbbl 32000 -K true -- -root /usr/lib64/erlang -progname erl -- -home /var/lib/rabbitmq -- -pa /usr/lib/rabbitmq/lib/rabbitmq_server-3.6.9/ebin -noshell -noinput -s rabbit boot -sname rabbit@rhel-7 -boot start_sasl -kernel inet_default_connect_options [{nodelay,true}] -sasl errlog_type error -sasl sasl_error_logger false -rabbit error_logger {file,\"/var/log/rabbitmq/<EMAIL>\"} -rabbit sasl_error_logger {file,\"/var/log/rabbitmq/<EMAIL>\"} -rabbit enabled_plugins_file \"/etc/rabbitmq/enabled_plugins\" -rabbit plugins_dir \"/usr/lib/rabbitmq/plugins:/usr/lib/rabbitmq/lib/rabbitmq_server-3.6.9/plugins\" -rabbit plugins_expand_dir \"/var/lib/rabbitmq/mnesia/rabbit@rhel-7-plugins-expand\" -os_mon start_cpu_sup false -os_mon start_disksup false -os_mon start_memsup false -mnesia dir \"/var/lib/rabbitmq/mnesia/rabbit@rhel-7\" -kernel inet_dist_listen_min 25672 -kernel inet_dist_listen_max 25672", "system.process.command": "/usr/lib64/erlang/erts-5.10.4/bin/beam.smp -W w -A 64 -P 1048576 -t 5000000 -stbt db -zdbbl 32000 -K true -- -root /usr/lib64/erlang -progname erl -- -home /var/lib/rabbitmq -- -pa /usr/lib/rabbitmq/lib/rabbitmq_server-3.6.9/ebin -noshell -noinput -s rabbit boot -sname rabbit@rhel-7 -boot start_sasl -kernel inet_default_connect_options [{nodelay,true}] -sasl errlog_type error -sasl sasl_error_logger false -rabbit error_logger {file,\"/var/log/rabbitmq/<EMAIL>\"} -rabbit sasl_error_logger {file,\"/var/log/rabbitmq/<EMAIL>\"} -rabbit enabled_plugins_file \"/etc/rabbitmq/enabled_plugins\" -rabbit plugins_dir \"/usr/lib/rabbitmq/plugins:/usr/lib/rabbitmq/lib/rabbitmq_server-3.6.9/plugins\" -rabbit plugins_expand_dir \"/var/lib/rabbitmq/mnesia/rabbit@rhel-7-plugins-expand\" -os_mon start_cpu_sup false -os_mon start_disksup false -os_mon start_memsup false -mnesia dir \"/var/lib/rabbitmq/mnesia/rabbit@rhel-7\" -kernel inet_dist_listen_min 25672 -kernel inet_dist_listen_max 25672", "system.process.cpu.percent": 1.125, "system.process.id": 908, "system.process.memory.used.bytes": 125861888, "system.process.memory.used.percent": 6.5, "system.process.name": "beam.smp", "system.process.started.time": " 141 days 22 hours 29 minutes 7 seconds", "system.process.started.time.seconds": 12263347, "system.process.threads": 73, "system.process.user": "rabbitmq", "system.process.virtual.memory.bytes": 2684014592}, {"status": "Up", "system.process": "named|/usr/sbin/named -u named -c /etc/named.conf", "system.process.command": "/usr/sbin/named -u named -c /etc/named.conf", "system.process.cpu.percent": 0, "system.process.id": 1227, "system.process.memory.used.bytes": 104968192, "system.process.memory.used.percent": 5.4, "system.process.name": "named", "system.process.started.time": " 141 days 22 hours 29 minutes 6 seconds", "system.process.started.time.seconds": 12263346, "system.process.threads": 7, "system.process.user": "named", "system.process.virtual.memory.bytes": 399429632}], "system.process.network.connection": [{"system.process": "beam.smp|/usr/lib64/erlang/erts-5.10.4/bin/beam.smp -W w -A 64 -P 1048576 -t 5000000 -stbt db -zdbbl 32000 -K true -- -root /usr/lib64/erlang -progname erl -- -home /var/lib/rabbitmq -- -pa /usr/lib/rabbitmq/lib/rabbitmq_server-3.6.9/ebin -noshell -noinput -s rabbit boot -sname rabbit@rhel-7 -boot start_sasl -kernel inet_default_connect_options [{nodelay,true}] -sasl errlog_type error -sasl sasl_error_logger false -rabbit error_logger {file,\"/var/log/rabbitmq/<EMAIL>\"} -rabbit sasl_error_logger {file,\"/var/log/rabbitmq/<EMAIL>\"} -rabbit enabled_plugins_file \"/etc/rabbitmq/enabled_plugins\" -rabbit plugins_dir \"/usr/lib/rabbitmq/plugins:/usr/lib/rabbitmq/lib/rabbitmq_server-3.6.9/plugins\" -rabbit plugins_expand_dir \"/var/lib/rabbitmq/mnesia/rabbit@rhel-7-plugins-expand\" -os_mon start_cpu_sup false -os_mon start_disksup false -os_mon start_memsup false -mnesia dir \"/var/lib/rabbitmq/mnesia/rabbit@rhel-7\" -kernel inet_dist_listen_min 25672 -kernel inet_dist_listen_max 25672", "system.process.destination.ip": "************", "system.process.destination.port": "*", "system.process.source.ip": "************", "system.process.source.port": "25672"}]}, {"correlation.metrics": ["system.process.network.connection"], "system.process": [{"status": "Up", "system.process": "beam.smp|/usr/lib64/erlang/erts-5.10.4/bin/beam.smp -W w -A 64 -P 1048576 -t 5000000 -stbt db -zdbbl 32000 -K true -- -root /usr/lib64/erlang -progname erl -- -home /var/lib/rabbitmq -- -pa /usr/lib/rabbitmq/lib/rabbitmq_server-3.6.9/ebin -noshell -noinput -s rabbit boot -sname rabbit@rhel-7 -boot start_sasl -kernel inet_default_connect_options [{nodelay,true}] -sasl errlog_type error -sasl sasl_error_logger false -rabbit error_logger {file,\"/var/log/rabbitmq/<EMAIL>\"} -rabbit sasl_error_logger {file,\"/var/log/rabbitmq/<EMAIL>\"} -rabbit enabled_plugins_file \"/etc/rabbitmq/enabled_plugins\" -rabbit plugins_dir \"/usr/lib/rabbitmq/plugins:/usr/lib/rabbitmq/lib/rabbitmq_server-3.6.9/plugins\" -rabbit plugins_expand_dir \"/var/lib/rabbitmq/mnesia/rabbit@rhel-7-plugins-expand\" -os_mon start_cpu_sup false -os_mon start_disksup false -os_mon start_memsup false -mnesia dir \"/var/lib/rabbitmq/mnesia/rabbit@rhel-7\" -kernel inet_dist_listen_min 25672 -kernel inet_dist_listen_max 25672", "system.process.command": "/usr/lib64/erlang/erts-5.10.4/bin/beam.smp -W w -A 64 -P 1048576 -t 5000000 -stbt db -zdbbl 32000 -K true -- -root /usr/lib64/erlang -progname erl -- -home /var/lib/rabbitmq -- -pa /usr/lib/rabbitmq/lib/rabbitmq_server-3.6.9/ebin -noshell -noinput -s rabbit boot -sname rabbit@rhel-7 -boot start_sasl -kernel inet_default_connect_options [{nodelay,true}] -sasl errlog_type error -sasl sasl_error_logger false -rabbit error_logger {file,\"/var/log/rabbitmq/<EMAIL>\"} -rabbit sasl_error_logger {file,\"/var/log/rabbitmq/<EMAIL>\"} -rabbit enabled_plugins_file \"/etc/rabbitmq/enabled_plugins\" -rabbit plugins_dir \"/usr/lib/rabbitmq/plugins:/usr/lib/rabbitmq/lib/rabbitmq_server-3.6.9/plugins\" -rabbit plugins_expand_dir \"/var/lib/rabbitmq/mnesia/rabbit@rhel-7-plugins-expand\" -os_mon start_cpu_sup false -os_mon start_disksup false -os_mon start_memsup false -mnesia dir \"/var/lib/rabbitmq/mnesia/rabbit@rhel-7\" -kernel inet_dist_listen_min 25672 -kernel inet_dist_listen_max 25672", "system.process.cpu.percent": 1.125, "system.process.id": 908, "system.process.memory.used.bytes": 115535872, "system.process.memory.used.percent": 5.9, "system.process.name": "beam.smp", "system.process.started.time": " 141 days 22 hours 34 minutes 8 seconds", "system.process.started.time.seconds": 12263648, "system.process.threads": 73, "system.process.user": "rabbitmq", "system.process.virtual.memory.bytes": 2683158528}, {"status": "Up", "system.process": "named|/usr/sbin/named -u named -c /etc/named.conf", "system.process.command": "/usr/sbin/named -u named -c /etc/named.conf", "system.process.cpu.percent": 0, "system.process.id": 1227, "system.process.memory.used.bytes": 104968192, "system.process.memory.used.percent": 5.4, "system.process.name": "named", "system.process.started.time": " 141 days 22 hours 34 minutes 7 seconds", "system.process.started.time.seconds": 12263647, "system.process.threads": 7, "system.process.user": "named", "system.process.virtual.memory.bytes": 399429632}], "system.process.network.connection": [{"system.process": "beam.smp|/usr/lib64/erlang/erts-5.10.4/bin/beam.smp -W w -A 64 -P 1048576 -t 5000000 -stbt db -zdbbl 32000 -K true -- -root /usr/lib64/erlang -progname erl -- -home /var/lib/rabbitmq -- -pa /usr/lib/rabbitmq/lib/rabbitmq_server-3.6.9/ebin -noshell -noinput -s rabbit boot -sname rabbit@rhel-7 -boot start_sasl -kernel inet_default_connect_options [{nodelay,true}] -sasl errlog_type error -sasl sasl_error_logger false -rabbit error_logger {file,\"/var/log/rabbitmq/<EMAIL>\"} -rabbit sasl_error_logger {file,\"/var/log/rabbitmq/<EMAIL>\"} -rabbit enabled_plugins_file \"/etc/rabbitmq/enabled_plugins\" -rabbit plugins_dir \"/usr/lib/rabbitmq/plugins:/usr/lib/rabbitmq/lib/rabbitmq_server-3.6.9/plugins\" -rabbit plugins_expand_dir \"/var/lib/rabbitmq/mnesia/rabbit@rhel-7-plugins-expand\" -os_mon start_cpu_sup false -os_mon start_disksup false -os_mon start_memsup false -mnesia dir \"/var/lib/rabbitmq/mnesia/rabbit@rhel-7\" -kernel inet_dist_listen_min 25672 -kernel inet_dist_listen_max 25672", "system.process.destination.ip": "************", "system.process.destination.port": "*", "system.process.source.ip": "************", "system.process.source.port": "25672"}]}, {"correlation.metrics": ["system.process.network.connection"], "system.process": [{"status": "Up", "system.process": "beam.smp|/usr/lib64/erlang/erts-5.10.4/bin/beam.smp -W w -A 64 -P 1048576 -t 5000000 -stbt db -zdbbl 32000 -K true -- -root /usr/lib64/erlang -progname erl -- -home /var/lib/rabbitmq -- -pa /usr/lib/rabbitmq/lib/rabbitmq_server-3.6.9/ebin -noshell -noinput -s rabbit boot -sname rabbit@rhel-7 -boot start_sasl -kernel inet_default_connect_options [{nodelay,true}] -sasl errlog_type error -sasl sasl_error_logger false -rabbit error_logger {file,\"/var/log/rabbitmq/<EMAIL>\"} -rabbit sasl_error_logger {file,\"/var/log/rabbitmq/<EMAIL>\"} -rabbit enabled_plugins_file \"/etc/rabbitmq/enabled_plugins\" -rabbit plugins_dir \"/usr/lib/rabbitmq/plugins:/usr/lib/rabbitmq/lib/rabbitmq_server-3.6.9/plugins\" -rabbit plugins_expand_dir \"/var/lib/rabbitmq/mnesia/rabbit@rhel-7-plugins-expand\" -os_mon start_cpu_sup false -os_mon start_disksup false -os_mon start_memsup false -mnesia dir \"/var/lib/rabbitmq/mnesia/rabbit@rhel-7\" -kernel inet_dist_listen_min 25672 -kernel inet_dist_listen_max 25672", "system.process.command": "/usr/lib64/erlang/erts-5.10.4/bin/beam.smp -W w -A 64 -P 1048576 -t 5000000 -stbt db -zdbbl 32000 -K true -- -root /usr/lib64/erlang -progname erl -- -home /var/lib/rabbitmq -- -pa /usr/lib/rabbitmq/lib/rabbitmq_server-3.6.9/ebin -noshell -noinput -s rabbit boot -sname rabbit@rhel-7 -boot start_sasl -kernel inet_default_connect_options [{nodelay,true}] -sasl errlog_type error -sasl sasl_error_logger false -rabbit error_logger {file,\"/var/log/rabbitmq/<EMAIL>\"} -rabbit sasl_error_logger {file,\"/var/log/rabbitmq/<EMAIL>\"} -rabbit enabled_plugins_file \"/etc/rabbitmq/enabled_plugins\" -rabbit plugins_dir \"/usr/lib/rabbitmq/plugins:/usr/lib/rabbitmq/lib/rabbitmq_server-3.6.9/plugins\" -rabbit plugins_expand_dir \"/var/lib/rabbitmq/mnesia/rabbit@rhel-7-plugins-expand\" -os_mon start_cpu_sup false -os_mon start_disksup false -os_mon start_memsup false -mnesia dir \"/var/lib/rabbitmq/mnesia/rabbit@rhel-7\" -kernel inet_dist_listen_min 25672 -kernel inet_dist_listen_max 25672", "system.process.cpu.percent": 1.125, "system.process.id": 908, "system.process.memory.used.bytes": 114348032, "system.process.memory.used.percent": 5.9, "system.process.name": "beam.smp", "system.process.started.time": " 141 days 22 hours 39 minutes 7 seconds", "system.process.started.time.seconds": 12263947, "system.process.threads": 73, "system.process.user": "rabbitmq", "system.process.virtual.memory.bytes": 2672701440}, {"status": "Up", "system.process": "named|/usr/sbin/named -u named -c /etc/named.conf", "system.process.command": "/usr/sbin/named -u named -c /etc/named.conf", "system.process.cpu.percent": 0, "system.process.id": 1227, "system.process.memory.used.bytes": 104968192, "system.process.memory.used.percent": 5.4, "system.process.name": "named", "system.process.started.time": " 141 days 22 hours 39 minutes 6 seconds", "system.process.started.time.seconds": 12263946, "system.process.threads": 7, "system.process.user": "named", "system.process.virtual.memory.bytes": 399429632}], "system.process.network.connection": [{"system.process": "beam.smp|/usr/lib64/erlang/erts-5.10.4/bin/beam.smp -W w -A 64 -P 1048576 -t 5000000 -stbt db -zdbbl 32000 -K true -- -root /usr/lib64/erlang -progname erl -- -home /var/lib/rabbitmq -- -pa /usr/lib/rabbitmq/lib/rabbitmq_server-3.6.9/ebin -noshell -noinput -s rabbit boot -sname rabbit@rhel-7 -boot start_sasl -kernel inet_default_connect_options [{nodelay,true}] -sasl errlog_type error -sasl sasl_error_logger false -rabbit error_logger {file,\"/var/log/rabbitmq/<EMAIL>\"} -rabbit sasl_error_logger {file,\"/var/log/rabbitmq/<EMAIL>\"} -rabbit enabled_plugins_file \"/etc/rabbitmq/enabled_plugins\" -rabbit plugins_dir \"/usr/lib/rabbitmq/plugins:/usr/lib/rabbitmq/lib/rabbitmq_server-3.6.9/plugins\" -rabbit plugins_expand_dir \"/var/lib/rabbitmq/mnesia/rabbit@rhel-7-plugins-expand\" -os_mon start_cpu_sup false -os_mon start_disksup false -os_mon start_memsup false -mnesia dir \"/var/lib/rabbitmq/mnesia/rabbit@rhel-7\" -kernel inet_dist_listen_min 25672 -kernel inet_dist_listen_max 25672", "system.process.destination.ip": "************", "system.process.destination.port": "*", "system.process.source.ip": "************", "system.process.source.port": "25672"}]}, {"correlation.metrics": ["system.process.network.connection"], "system.process": [{"status": "Up", "system.process": "beam.smp|/usr/lib64/erlang/erts-5.10.4/bin/beam.smp -W w -A 64 -P 1048576 -t 5000000 -stbt db -zdbbl 32000 -K true -- -root /usr/lib64/erlang -progname erl -- -home /var/lib/rabbitmq -- -pa /usr/lib/rabbitmq/lib/rabbitmq_server-3.6.9/ebin -noshell -noinput -s rabbit boot -sname rabbit@rhel-7 -boot start_sasl -kernel inet_default_connect_options [{nodelay,true}] -sasl errlog_type error -sasl sasl_error_logger false -rabbit error_logger {file,\"/var/log/rabbitmq/<EMAIL>\"} -rabbit sasl_error_logger {file,\"/var/log/rabbitmq/<EMAIL>\"} -rabbit enabled_plugins_file \"/etc/rabbitmq/enabled_plugins\" -rabbit plugins_dir \"/usr/lib/rabbitmq/plugins:/usr/lib/rabbitmq/lib/rabbitmq_server-3.6.9/plugins\" -rabbit plugins_expand_dir \"/var/lib/rabbitmq/mnesia/rabbit@rhel-7-plugins-expand\" -os_mon start_cpu_sup false -os_mon start_disksup false -os_mon start_memsup false -mnesia dir \"/var/lib/rabbitmq/mnesia/rabbit@rhel-7\" -kernel inet_dist_listen_min 25672 -kernel inet_dist_listen_max 25672", "system.process.command": "/usr/lib64/erlang/erts-5.10.4/bin/beam.smp -W w -A 64 -P 1048576 -t 5000000 -stbt db -zdbbl 32000 -K true -- -root /usr/lib64/erlang -progname erl -- -home /var/lib/rabbitmq -- -pa /usr/lib/rabbitmq/lib/rabbitmq_server-3.6.9/ebin -noshell -noinput -s rabbit boot -sname rabbit@rhel-7 -boot start_sasl -kernel inet_default_connect_options [{nodelay,true}] -sasl errlog_type error -sasl sasl_error_logger false -rabbit error_logger {file,\"/var/log/rabbitmq/<EMAIL>\"} -rabbit sasl_error_logger {file,\"/var/log/rabbitmq/<EMAIL>\"} -rabbit enabled_plugins_file \"/etc/rabbitmq/enabled_plugins\" -rabbit plugins_dir \"/usr/lib/rabbitmq/plugins:/usr/lib/rabbitmq/lib/rabbitmq_server-3.6.9/plugins\" -rabbit plugins_expand_dir \"/var/lib/rabbitmq/mnesia/rabbit@rhel-7-plugins-expand\" -os_mon start_cpu_sup false -os_mon start_disksup false -os_mon start_memsup false -mnesia dir \"/var/lib/rabbitmq/mnesia/rabbit@rhel-7\" -kernel inet_dist_listen_min 25672 -kernel inet_dist_listen_max 25672", "system.process.cpu.percent": 1.125, "system.process.id": 908, "system.process.memory.used.bytes": 134475776, "system.process.memory.used.percent": 6.9, "system.process.name": "beam.smp", "system.process.started.time": " 141 days 22 hours 44 minutes 7 seconds", "system.process.started.time.seconds": 12264247, "system.process.threads": 73, "system.process.user": "rabbitmq", "system.process.virtual.memory.bytes": 2691514368}, {"status": "Up", "system.process": "named|/usr/sbin/named -u named -c /etc/named.conf", "system.process.command": "/usr/sbin/named -u named -c /etc/named.conf", "system.process.cpu.percent": 0, "system.process.id": 1227, "system.process.memory.used.bytes": 104968192, "system.process.memory.used.percent": 5.4, "system.process.name": "named", "system.process.started.time": " 141 days 22 hours 44 minutes 6 seconds", "system.process.started.time.seconds": 12264246, "system.process.threads": 7, "system.process.user": "named", "system.process.virtual.memory.bytes": 399429632}], "system.process.network.connection": [{"system.process": "beam.smp|/usr/lib64/erlang/erts-5.10.4/bin/beam.smp -W w -A 64 -P 1048576 -t 5000000 -stbt db -zdbbl 32000 -K true -- -root /usr/lib64/erlang -progname erl -- -home /var/lib/rabbitmq -- -pa /usr/lib/rabbitmq/lib/rabbitmq_server-3.6.9/ebin -noshell -noinput -s rabbit boot -sname rabbit@rhel-7 -boot start_sasl -kernel inet_default_connect_options [{nodelay,true}] -sasl errlog_type error -sasl sasl_error_logger false -rabbit error_logger {file,\"/var/log/rabbitmq/<EMAIL>\"} -rabbit sasl_error_logger {file,\"/var/log/rabbitmq/<EMAIL>\"} -rabbit enabled_plugins_file \"/etc/rabbitmq/enabled_plugins\" -rabbit plugins_dir \"/usr/lib/rabbitmq/plugins:/usr/lib/rabbitmq/lib/rabbitmq_server-3.6.9/plugins\" -rabbit plugins_expand_dir \"/var/lib/rabbitmq/mnesia/rabbit@rhel-7-plugins-expand\" -os_mon start_cpu_sup false -os_mon start_disksup false -os_mon start_memsup false -mnesia dir \"/var/lib/rabbitmq/mnesia/rabbit@rhel-7\" -kernel inet_dist_listen_min 25672 -kernel inet_dist_listen_max 25672", "system.process.destination.ip": "************", "system.process.destination.port": "*", "system.process.source.ip": "************", "system.process.source.port": "25672"}]}, {"correlation.metrics": ["system.process.network.connection"], "system.process": [{"status": "Up", "system.process": "beam.smp|/usr/lib64/erlang/erts-5.10.4/bin/beam.smp -W w -A 64 -P 1048576 -t 5000000 -stbt db -zdbbl 32000 -K true -- -root /usr/lib64/erlang -progname erl -- -home /var/lib/rabbitmq -- -pa /usr/lib/rabbitmq/lib/rabbitmq_server-3.6.9/ebin -noshell -noinput -s rabbit boot -sname rabbit@rhel-7 -boot start_sasl -kernel inet_default_connect_options [{nodelay,true}] -sasl errlog_type error -sasl sasl_error_logger false -rabbit error_logger {file,\"/var/log/rabbitmq/<EMAIL>\"} -rabbit sasl_error_logger {file,\"/var/log/rabbitmq/<EMAIL>\"} -rabbit enabled_plugins_file \"/etc/rabbitmq/enabled_plugins\" -rabbit plugins_dir \"/usr/lib/rabbitmq/plugins:/usr/lib/rabbitmq/lib/rabbitmq_server-3.6.9/plugins\" -rabbit plugins_expand_dir \"/var/lib/rabbitmq/mnesia/rabbit@rhel-7-plugins-expand\" -os_mon start_cpu_sup false -os_mon start_disksup false -os_mon start_memsup false -mnesia dir \"/var/lib/rabbitmq/mnesia/rabbit@rhel-7\" -kernel inet_dist_listen_min 25672 -kernel inet_dist_listen_max 25672", "system.process.command": "/usr/lib64/erlang/erts-5.10.4/bin/beam.smp -W w -A 64 -P 1048576 -t 5000000 -stbt db -zdbbl 32000 -K true -- -root /usr/lib64/erlang -progname erl -- -home /var/lib/rabbitmq -- -pa /usr/lib/rabbitmq/lib/rabbitmq_server-3.6.9/ebin -noshell -noinput -s rabbit boot -sname rabbit@rhel-7 -boot start_sasl -kernel inet_default_connect_options [{nodelay,true}] -sasl errlog_type error -sasl sasl_error_logger false -rabbit error_logger {file,\"/var/log/rabbitmq/<EMAIL>\"} -rabbit sasl_error_logger {file,\"/var/log/rabbitmq/<EMAIL>\"} -rabbit enabled_plugins_file \"/etc/rabbitmq/enabled_plugins\" -rabbit plugins_dir \"/usr/lib/rabbitmq/plugins:/usr/lib/rabbitmq/lib/rabbitmq_server-3.6.9/plugins\" -rabbit plugins_expand_dir \"/var/lib/rabbitmq/mnesia/rabbit@rhel-7-plugins-expand\" -os_mon start_cpu_sup false -os_mon start_disksup false -os_mon start_memsup false -mnesia dir \"/var/lib/rabbitmq/mnesia/rabbit@rhel-7\" -kernel inet_dist_listen_min 25672 -kernel inet_dist_listen_max 25672", "system.process.cpu.percent": 1.125, "system.process.id": 908, "system.process.memory.used.bytes": 125693952, "system.process.memory.used.percent": 6.5, "system.process.name": "beam.smp", "system.process.started.time": " 141 days 22 hours 49 minutes 6 seconds", "system.process.started.time.seconds": 12264546, "system.process.threads": 73, "system.process.user": "rabbitmq", "system.process.virtual.memory.bytes": 2675699712}, {"status": "Up", "system.process": "named|/usr/sbin/named -u named -c /etc/named.conf", "system.process.command": "/usr/sbin/named -u named -c /etc/named.conf", "system.process.cpu.percent": 0, "system.process.id": 1227, "system.process.memory.used.bytes": 104968192, "system.process.memory.used.percent": 5.4, "system.process.name": "named", "system.process.started.time": " 141 days 22 hours 49 minutes 5 seconds", "system.process.started.time.seconds": 12264545, "system.process.threads": 7, "system.process.user": "named", "system.process.virtual.memory.bytes": 399429632}], "system.process.network.connection": [{"system.process": "beam.smp|/usr/lib64/erlang/erts-5.10.4/bin/beam.smp -W w -A 64 -P 1048576 -t 5000000 -stbt db -zdbbl 32000 -K true -- -root /usr/lib64/erlang -progname erl -- -home /var/lib/rabbitmq -- -pa /usr/lib/rabbitmq/lib/rabbitmq_server-3.6.9/ebin -noshell -noinput -s rabbit boot -sname rabbit@rhel-7 -boot start_sasl -kernel inet_default_connect_options [{nodelay,true}] -sasl errlog_type error -sasl sasl_error_logger false -rabbit error_logger {file,\"/var/log/rabbitmq/<EMAIL>\"} -rabbit sasl_error_logger {file,\"/var/log/rabbitmq/<EMAIL>\"} -rabbit enabled_plugins_file \"/etc/rabbitmq/enabled_plugins\" -rabbit plugins_dir \"/usr/lib/rabbitmq/plugins:/usr/lib/rabbitmq/lib/rabbitmq_server-3.6.9/plugins\" -rabbit plugins_expand_dir \"/var/lib/rabbitmq/mnesia/rabbit@rhel-7-plugins-expand\" -os_mon start_cpu_sup false -os_mon start_disksup false -os_mon start_memsup false -mnesia dir \"/var/lib/rabbitmq/mnesia/rabbit@rhel-7\" -kernel inet_dist_listen_min 25672 -kernel inet_dist_listen_max 25672", "system.process.destination.ip": "************", "system.process.destination.port": "*", "system.process.source.ip": "************", "system.process.source.port": "25672"}]}, {"correlation.metrics": ["system.process.network.connection"], "system.process": [{"status": "Up", "system.process": "beam.smp|/usr/lib64/erlang/erts-5.10.4/bin/beam.smp -W w -A 64 -P 1048576 -t 5000000 -stbt db -zdbbl 32000 -K true -- -root /usr/lib64/erlang -progname erl -- -home /var/lib/rabbitmq -- -pa /usr/lib/rabbitmq/lib/rabbitmq_server-3.6.9/ebin -noshell -noinput -s rabbit boot -sname rabbit@rhel-7 -boot start_sasl -kernel inet_default_connect_options [{nodelay,true}] -sasl errlog_type error -sasl sasl_error_logger false -rabbit error_logger {file,\"/var/log/rabbitmq/<EMAIL>\"} -rabbit sasl_error_logger {file,\"/var/log/rabbitmq/<EMAIL>\"} -rabbit enabled_plugins_file \"/etc/rabbitmq/enabled_plugins\" -rabbit plugins_dir \"/usr/lib/rabbitmq/plugins:/usr/lib/rabbitmq/lib/rabbitmq_server-3.6.9/plugins\" -rabbit plugins_expand_dir \"/var/lib/rabbitmq/mnesia/rabbit@rhel-7-plugins-expand\" -os_mon start_cpu_sup false -os_mon start_disksup false -os_mon start_memsup false -mnesia dir \"/var/lib/rabbitmq/mnesia/rabbit@rhel-7\" -kernel inet_dist_listen_min 25672 -kernel inet_dist_listen_max 25672", "system.process.command": "/usr/lib64/erlang/erts-5.10.4/bin/beam.smp -W w -A 64 -P 1048576 -t 5000000 -stbt db -zdbbl 32000 -K true -- -root /usr/lib64/erlang -progname erl -- -home /var/lib/rabbitmq -- -pa /usr/lib/rabbitmq/lib/rabbitmq_server-3.6.9/ebin -noshell -noinput -s rabbit boot -sname rabbit@rhel-7 -boot start_sasl -kernel inet_default_connect_options [{nodelay,true}] -sasl errlog_type error -sasl sasl_error_logger false -rabbit error_logger {file,\"/var/log/rabbitmq/<EMAIL>\"} -rabbit sasl_error_logger {file,\"/var/log/rabbitmq/<EMAIL>\"} -rabbit enabled_plugins_file \"/etc/rabbitmq/enabled_plugins\" -rabbit plugins_dir \"/usr/lib/rabbitmq/plugins:/usr/lib/rabbitmq/lib/rabbitmq_server-3.6.9/plugins\" -rabbit plugins_expand_dir \"/var/lib/rabbitmq/mnesia/rabbit@rhel-7-plugins-expand\" -os_mon start_cpu_sup false -os_mon start_disksup false -os_mon start_memsup false -mnesia dir \"/var/lib/rabbitmq/mnesia/rabbit@rhel-7\" -kernel inet_dist_listen_min 25672 -kernel inet_dist_listen_max 25672", "system.process.cpu.percent": 1.125, "system.process.id": 908, "system.process.memory.used.bytes": 130895872, "system.process.memory.used.percent": 6.7, "system.process.name": "beam.smp", "system.process.started.time": " 141 days 22 hours 54 minutes 8 seconds", "system.process.started.time.seconds": 12264848, "system.process.threads": 73, "system.process.user": "rabbitmq", "system.process.virtual.memory.bytes": 2689048576}, {"status": "Up", "system.process": "named|/usr/sbin/named -u named -c /etc/named.conf", "system.process.command": "/usr/sbin/named -u named -c /etc/named.conf", "system.process.cpu.percent": 0, "system.process.id": 1227, "system.process.memory.used.bytes": 104968192, "system.process.memory.used.percent": 5.4, "system.process.name": "named", "system.process.started.time": " 141 days 22 hours 54 minutes 7 seconds", "system.process.started.time.seconds": 12264847, "system.process.threads": 7, "system.process.user": "named", "system.process.virtual.memory.bytes": 399429632}], "system.process.network.connection": [{"system.process": "beam.smp|/usr/lib64/erlang/erts-5.10.4/bin/beam.smp -W w -A 64 -P 1048576 -t 5000000 -stbt db -zdbbl 32000 -K true -- -root /usr/lib64/erlang -progname erl -- -home /var/lib/rabbitmq -- -pa /usr/lib/rabbitmq/lib/rabbitmq_server-3.6.9/ebin -noshell -noinput -s rabbit boot -sname rabbit@rhel-7 -boot start_sasl -kernel inet_default_connect_options [{nodelay,true}] -sasl errlog_type error -sasl sasl_error_logger false -rabbit error_logger {file,\"/var/log/rabbitmq/<EMAIL>\"} -rabbit sasl_error_logger {file,\"/var/log/rabbitmq/<EMAIL>\"} -rabbit enabled_plugins_file \"/etc/rabbitmq/enabled_plugins\" -rabbit plugins_dir \"/usr/lib/rabbitmq/plugins:/usr/lib/rabbitmq/lib/rabbitmq_server-3.6.9/plugins\" -rabbit plugins_expand_dir \"/var/lib/rabbitmq/mnesia/rabbit@rhel-7-plugins-expand\" -os_mon start_cpu_sup false -os_mon start_disksup false -os_mon start_memsup false -mnesia dir \"/var/lib/rabbitmq/mnesia/rabbit@rhel-7\" -kernel inet_dist_listen_min 25672 -kernel inet_dist_listen_max 25672", "system.process.destination.ip": "************", "system.process.destination.port": "*", "system.process.source.ip": "************", "system.process.source.port": "25672"}]}, {"correlation.metrics": ["system.process.network.connection"], "system.process": [{"status": "Up", "system.process": "beam.smp|/usr/lib64/erlang/erts-5.10.4/bin/beam.smp -W w -A 64 -P 1048576 -t 5000000 -stbt db -zdbbl 32000 -K true -- -root /usr/lib64/erlang -progname erl -- -home /var/lib/rabbitmq -- -pa /usr/lib/rabbitmq/lib/rabbitmq_server-3.6.9/ebin -noshell -noinput -s rabbit boot -sname rabbit@rhel-7 -boot start_sasl -kernel inet_default_connect_options [{nodelay,true}] -sasl errlog_type error -sasl sasl_error_logger false -rabbit error_logger {file,\"/var/log/rabbitmq/<EMAIL>\"} -rabbit sasl_error_logger {file,\"/var/log/rabbitmq/<EMAIL>\"} -rabbit enabled_plugins_file \"/etc/rabbitmq/enabled_plugins\" -rabbit plugins_dir \"/usr/lib/rabbitmq/plugins:/usr/lib/rabbitmq/lib/rabbitmq_server-3.6.9/plugins\" -rabbit plugins_expand_dir \"/var/lib/rabbitmq/mnesia/rabbit@rhel-7-plugins-expand\" -os_mon start_cpu_sup false -os_mon start_disksup false -os_mon start_memsup false -mnesia dir \"/var/lib/rabbitmq/mnesia/rabbit@rhel-7\" -kernel inet_dist_listen_min 25672 -kernel inet_dist_listen_max 25672", "system.process.command": "/usr/lib64/erlang/erts-5.10.4/bin/beam.smp -W w -A 64 -P 1048576 -t 5000000 -stbt db -zdbbl 32000 -K true -- -root /usr/lib64/erlang -progname erl -- -home /var/lib/rabbitmq -- -pa /usr/lib/rabbitmq/lib/rabbitmq_server-3.6.9/ebin -noshell -noinput -s rabbit boot -sname rabbit@rhel-7 -boot start_sasl -kernel inet_default_connect_options [{nodelay,true}] -sasl errlog_type error -sasl sasl_error_logger false -rabbit error_logger {file,\"/var/log/rabbitmq/<EMAIL>\"} -rabbit sasl_error_logger {file,\"/var/log/rabbitmq/<EMAIL>\"} -rabbit enabled_plugins_file \"/etc/rabbitmq/enabled_plugins\" -rabbit plugins_dir \"/usr/lib/rabbitmq/plugins:/usr/lib/rabbitmq/lib/rabbitmq_server-3.6.9/plugins\" -rabbit plugins_expand_dir \"/var/lib/rabbitmq/mnesia/rabbit@rhel-7-plugins-expand\" -os_mon start_cpu_sup false -os_mon start_disksup false -os_mon start_memsup false -mnesia dir \"/var/lib/rabbitmq/mnesia/rabbit@rhel-7\" -kernel inet_dist_listen_min 25672 -kernel inet_dist_listen_max 25672", "system.process.cpu.percent": 1.125, "system.process.id": 908, "system.process.memory.used.bytes": 122286080, "system.process.memory.used.percent": 6.3, "system.process.name": "beam.smp", "system.process.started.time": " 141 days 23 hours 4 minutes 8 seconds", "system.process.started.time.seconds": 12265448, "system.process.threads": 73, "system.process.user": "rabbitmq", "system.process.virtual.memory.bytes": 2682888192}, {"status": "Up", "system.process": "named|/usr/sbin/named -u named -c /etc/named.conf", "system.process.command": "/usr/sbin/named -u named -c /etc/named.conf", "system.process.cpu.percent": 0, "system.process.id": 1227, "system.process.memory.used.bytes": 104968192, "system.process.memory.used.percent": 5.4, "system.process.name": "named", "system.process.started.time": " 141 days 23 hours 4 minutes 7 seconds", "system.process.started.time.seconds": 12265447, "system.process.threads": 7, "system.process.user": "named", "system.process.virtual.memory.bytes": 399429632}], "system.process.network.connection": [{"system.process": "beam.smp|/usr/lib64/erlang/erts-5.10.4/bin/beam.smp -W w -A 64 -P 1048576 -t 5000000 -stbt db -zdbbl 32000 -K true -- -root /usr/lib64/erlang -progname erl -- -home /var/lib/rabbitmq -- -pa /usr/lib/rabbitmq/lib/rabbitmq_server-3.6.9/ebin -noshell -noinput -s rabbit boot -sname rabbit@rhel-7 -boot start_sasl -kernel inet_default_connect_options [{nodelay,true}] -sasl errlog_type error -sasl sasl_error_logger false -rabbit error_logger {file,\"/var/log/rabbitmq/<EMAIL>\"} -rabbit sasl_error_logger {file,\"/var/log/rabbitmq/<EMAIL>\"} -rabbit enabled_plugins_file \"/etc/rabbitmq/enabled_plugins\" -rabbit plugins_dir \"/usr/lib/rabbitmq/plugins:/usr/lib/rabbitmq/lib/rabbitmq_server-3.6.9/plugins\" -rabbit plugins_expand_dir \"/var/lib/rabbitmq/mnesia/rabbit@rhel-7-plugins-expand\" -os_mon start_cpu_sup false -os_mon start_disksup false -os_mon start_memsup false -mnesia dir \"/var/lib/rabbitmq/mnesia/rabbit@rhel-7\" -kernel inet_dist_listen_min 25672 -kernel inet_dist_listen_max 25672", "system.process.destination.ip": "************", "system.process.destination.port": "*", "system.process.source.ip": "************", "system.process.source.port": "25672"}]}], "rediscovery": null}, "************": {"discovery": null, "collect": [{"correlation.metrics": ["system.process.network.connection"], "system.process": [{"status": "Up", "system.process": "postgres|/usr/lib/postgresql/16/bin/postgres -D /var/lib/postgresql/16/main -c config_file=/etc/postgresql/16/main/postgresql.conf", "system.process.command": "/usr/lib/postgresql/16/bin/postgres -D /var/lib/postgresql/16/main -c config_file=/etc/postgresql/16/main/postgresql.conf", "system.process.cpu.percent": 0, "system.process.handles": 66, "system.process.id": 915584, "system.process.memory.used.bytes": 18481152, "system.process.memory.used.percent": 0.1, "system.process.name": "postgres", "system.process.started.time": " 20 days 3 hours 46 minutes 9 seconds", "system.process.started.time.seconds": 1741569, "system.process.threads": 1, "system.process.user": "postgres", "system.process.virtual.memory.bytes": 228593664}, {"status": "Up", "system.process": "postgres|postgres: 16/main: logger", "system.process.command": "postgres: 16/main: logger", "system.process.cpu.percent": 0, "system.process.handles": 61, "system.process.id": 915585, "system.process.memory.used.bytes": 4444160, "system.process.memory.used.percent": 0, "system.process.name": "postgres", "system.process.started.time": " 20 days 3 hours 46 minutes 9 seconds", "system.process.started.time.seconds": 1741569, "system.process.threads": 1, "system.process.user": "postgres", "system.process.virtual.memory.bytes": 79015936}, {"status": "Up", "system.process": "postgres|postgres: 16/main: checkpointer", "system.process.command": "postgres: 16/main: checkpointer", "system.process.cpu.percent": 0, "system.process.handles": 62, "system.process.id": 915586, "system.process.memory.used.bytes": 46018560, "system.process.memory.used.percent": 0.3, "system.process.name": "postgres", "system.process.started.time": " 20 days 3 hours 46 minutes 9 seconds", "system.process.started.time.seconds": 1741569, "system.process.threads": 1, "system.process.user": "postgres", "system.process.virtual.memory.bytes": 229076992}, {"status": "Up", "system.process": "postgres|postgres: 16/main: background writer", "system.process.command": "postgres: 16/main: background writer", "system.process.cpu.percent": 0, "system.process.handles": 61, "system.process.id": 915587, "system.process.memory.used.bytes": 10760192, "system.process.memory.used.percent": 0, "system.process.name": "postgres", "system.process.started.time": " 20 days 3 hours 46 minutes 9 seconds", "system.process.started.time.seconds": 1741569, "system.process.threads": 1, "system.process.user": "postgres", "system.process.virtual.memory.bytes": 228745216}, {"status": "Up", "system.process": "postgres|postgres: 16/main: walwriter", "system.process.command": "postgres: 16/main: walwriter", "system.process.cpu.percent": 0, "system.process.handles": 62, "system.process.id": 915591, "system.process.memory.used.bytes": 8925184, "system.process.memory.used.percent": 0, "system.process.name": "postgres", "system.process.started.time": " 20 days 3 hours 46 minutes 9 seconds", "system.process.started.time.seconds": 1741569, "system.process.threads": 1, "system.process.user": "postgres", "system.process.virtual.memory.bytes": 228593664}, {"status": "Up", "system.process": "postgres|postgres: 16/main: autovacuum launcher", "system.process.command": "postgres: 16/main: autovacuum launcher", "system.process.cpu.percent": 0, "system.process.handles": 63, "system.process.id": 915592, "system.process.memory.used.bytes": 8663040, "system.process.memory.used.percent": 0, "system.process.name": "postgres", "system.process.started.time": " 20 days 3 hours 46 minutes 9 seconds", "system.process.started.time.seconds": 1741569, "system.process.threads": 1, "system.process.user": "postgres", "system.process.virtual.memory.bytes": 230232064}, {"status": "Up", "system.process": "postgres|postgres: 16/main: logical replication launcher", "system.process.command": "postgres: 16/main: logical replication launcher", "system.process.cpu.percent": 0, "system.process.handles": 63, "system.process.id": 915593, "system.process.memory.used.bytes": 5910528, "system.process.memory.used.percent": 0, "system.process.name": "postgres", "system.process.started.time": " 20 days 3 hours 46 minutes 9 seconds", "system.process.started.time.seconds": 1741569, "system.process.threads": 1, "system.process.user": "postgres", "system.process.virtual.memory.bytes": 230207488}, {"status": "Down", "system.process": "sudo|sudo -u postgres psql", "system.process.command": "sudo -u postgres psql", "system.process.name": "sudo"}, {"status": "Down", "system.process": "psql|/usr/lib/postgresql/16/bin/psql", "system.process.command": "/usr/lib/postgresql/16/bin/psql", "system.process.name": "psql"}, {"status": "Down", "system.process": "postgres|postgres: 16/main: motadata motadata 127.0.0.1(53396) idle", "system.process.command": "postgres: 16/main: motadata motadata 127.0.0.1(53396) idle", "system.process.name": "postgres"}, {"status": "Down", "system.process": "postgres|postgres: 16/main: motadata motadata 127.0.0.1(52362) idle", "system.process.command": "postgres: 16/main: motadata motadata 127.0.0.1(52362) idle", "system.process.name": "postgres"}], "system.process.network.connection": [{"system.process": "postgres|/usr/lib/postgresql/16/bin/postgres -D /var/lib/postgresql/16/main -c config_file=/etc/postgresql/16/main/postgresql.conf", "system.process.destination.ip": "************", "system.process.destination.port": "*", "system.process.source.ip": "************", "system.process.source.port": "5432"}]}, {"correlation.metrics": ["system.process.network.connection"], "system.process": [{"status": "Up", "system.process": "postgres|postgres: 16/main: logger", "system.process.command": "postgres: 16/main: logger", "system.process.cpu.percent": 0, "system.process.handles": 61, "system.process.id": 915585, "system.process.memory.used.bytes": 4444160, "system.process.memory.used.percent": 0, "system.process.name": "postgres", "system.process.started.time": " 20 days 3 hours 48 minutes 12 seconds", "system.process.started.time.seconds": 1741692, "system.process.threads": 1, "system.process.user": "postgres", "system.process.virtual.memory.bytes": 79015936}, {"status": "Up", "system.process": "postgres|postgres: 16/main: checkpointer", "system.process.command": "postgres: 16/main: checkpointer", "system.process.cpu.percent": 0, "system.process.handles": 62, "system.process.id": 915586, "system.process.memory.used.bytes": 46018560, "system.process.memory.used.percent": 0.3, "system.process.name": "postgres", "system.process.started.time": " 20 days 3 hours 48 minutes 12 seconds", "system.process.started.time.seconds": 1741692, "system.process.threads": 1, "system.process.user": "postgres", "system.process.virtual.memory.bytes": 229076992}, {"status": "Up", "system.process": "postgres|postgres: 16/main: background writer", "system.process.command": "postgres: 16/main: background writer", "system.process.cpu.percent": 0, "system.process.handles": 61, "system.process.id": 915587, "system.process.memory.used.bytes": 10760192, "system.process.memory.used.percent": 0, "system.process.name": "postgres", "system.process.started.time": " 20 days 3 hours 48 minutes 12 seconds", "system.process.started.time.seconds": 1741692, "system.process.threads": 1, "system.process.user": "postgres", "system.process.virtual.memory.bytes": 228745216}, {"status": "Up", "system.process": "postgres|postgres: 16/main: walwriter", "system.process.command": "postgres: 16/main: walwriter", "system.process.cpu.percent": 0, "system.process.handles": 62, "system.process.id": 915591, "system.process.memory.used.bytes": 8925184, "system.process.memory.used.percent": 0, "system.process.name": "postgres", "system.process.started.time": " 20 days 3 hours 48 minutes 12 seconds", "system.process.started.time.seconds": 1741692, "system.process.threads": 1, "system.process.user": "postgres", "system.process.virtual.memory.bytes": 228593664}, {"status": "Up", "system.process": "postgres|postgres: 16/main: autovacuum launcher", "system.process.command": "postgres: 16/main: autovacuum launcher", "system.process.cpu.percent": 0, "system.process.handles": 63, "system.process.id": 915592, "system.process.memory.used.bytes": 8663040, "system.process.memory.used.percent": 0, "system.process.name": "postgres", "system.process.started.time": " 20 days 3 hours 48 minutes 12 seconds", "system.process.started.time.seconds": 1741692, "system.process.threads": 1, "system.process.user": "postgres", "system.process.virtual.memory.bytes": 230232064}, {"status": "Up", "system.process": "postgres|postgres: 16/main: logical replication launcher", "system.process.command": "postgres: 16/main: logical replication launcher", "system.process.cpu.percent": 0, "system.process.handles": 63, "system.process.id": 915593, "system.process.memory.used.bytes": 5910528, "system.process.memory.used.percent": 0, "system.process.name": "postgres", "system.process.started.time": " 20 days 3 hours 48 minutes 12 seconds", "system.process.started.time.seconds": 1741692, "system.process.threads": 1, "system.process.user": "postgres", "system.process.virtual.memory.bytes": 230207488}, {"status": "Up", "system.process": "postgres|/usr/lib/postgresql/16/bin/postgres -D /var/lib/postgresql/16/main -c config_file=/etc/postgresql/16/main/postgresql.conf", "system.process.command": "/usr/lib/postgresql/16/bin/postgres -D /var/lib/postgresql/16/main -c config_file=/etc/postgresql/16/main/postgresql.conf", "system.process.cpu.percent": 0, "system.process.handles": 66, "system.process.id": 915584, "system.process.memory.used.bytes": 18481152, "system.process.memory.used.percent": 0.1, "system.process.name": "postgres", "system.process.started.time": " 20 days 3 hours 48 minutes 12 seconds", "system.process.started.time.seconds": 1741692, "system.process.threads": 1, "system.process.user": "postgres", "system.process.virtual.memory.bytes": 228593664}, {"status": "Down", "system.process": "sudo|sudo -u postgres psql", "system.process.command": "sudo -u postgres psql", "system.process.name": "sudo"}, {"status": "Down", "system.process": "psql|/usr/lib/postgresql/16/bin/psql", "system.process.command": "/usr/lib/postgresql/16/bin/psql", "system.process.name": "psql"}, {"status": "Down", "system.process": "postgres|postgres: 16/main: motadata motadata 127.0.0.1(53396) idle", "system.process.command": "postgres: 16/main: motadata motadata 127.0.0.1(53396) idle", "system.process.name": "postgres"}, {"status": "Down", "system.process": "postgres|postgres: 16/main: motadata motadata 127.0.0.1(52362) idle", "system.process.command": "postgres: 16/main: motadata motadata 127.0.0.1(52362) idle", "system.process.name": "postgres"}], "system.process.network.connection": [{"system.process": "postgres|/usr/lib/postgresql/16/bin/postgres -D /var/lib/postgresql/16/main -c config_file=/etc/postgresql/16/main/postgresql.conf", "system.process.destination.ip": "************", "system.process.destination.port": "*", "system.process.source.ip": "************", "system.process.source.port": "5432"}]}, {"correlation.metrics": ["system.process.network.connection"], "system.process": [{"status": "Up", "system.process": "postgres|postgres: 16/main: walwriter", "system.process.command": "postgres: 16/main: walwriter", "system.process.cpu.percent": 0, "system.process.handles": 62, "system.process.id": 915591, "system.process.memory.used.bytes": 8925184, "system.process.memory.used.percent": 0, "system.process.name": "postgres", "system.process.started.time": " 20 days 3 hours 53 minutes 12 seconds", "system.process.started.time.seconds": 1741992, "system.process.threads": 1, "system.process.user": "postgres", "system.process.virtual.memory.bytes": 228593664}, {"status": "Up", "system.process": "postgres|postgres: 16/main: autovacuum launcher", "system.process.command": "postgres: 16/main: autovacuum launcher", "system.process.cpu.percent": 0, "system.process.handles": 63, "system.process.id": 915592, "system.process.memory.used.bytes": 8663040, "system.process.memory.used.percent": 0, "system.process.name": "postgres", "system.process.started.time": " 20 days 3 hours 53 minutes 12 seconds", "system.process.started.time.seconds": 1741992, "system.process.threads": 1, "system.process.user": "postgres", "system.process.virtual.memory.bytes": 230232064}, {"status": "Up", "system.process": "postgres|postgres: 16/main: logical replication launcher", "system.process.command": "postgres: 16/main: logical replication launcher", "system.process.cpu.percent": 0, "system.process.handles": 63, "system.process.id": 915593, "system.process.memory.used.bytes": 5910528, "system.process.memory.used.percent": 0, "system.process.name": "postgres", "system.process.started.time": " 20 days 3 hours 53 minutes 12 seconds", "system.process.started.time.seconds": 1741992, "system.process.threads": 1, "system.process.user": "postgres", "system.process.virtual.memory.bytes": 230207488}, {"status": "Up", "system.process": "postgres|/usr/lib/postgresql/16/bin/postgres -D /var/lib/postgresql/16/main -c config_file=/etc/postgresql/16/main/postgresql.conf", "system.process.command": "/usr/lib/postgresql/16/bin/postgres -D /var/lib/postgresql/16/main -c config_file=/etc/postgresql/16/main/postgresql.conf", "system.process.cpu.percent": 0, "system.process.handles": 66, "system.process.id": 915584, "system.process.memory.used.bytes": 18481152, "system.process.memory.used.percent": 0.1, "system.process.name": "postgres", "system.process.started.time": " 20 days 3 hours 53 minutes 12 seconds", "system.process.started.time.seconds": 1741992, "system.process.threads": 1, "system.process.user": "postgres", "system.process.virtual.memory.bytes": 228593664}, {"status": "Up", "system.process": "postgres|postgres: 16/main: logger", "system.process.command": "postgres: 16/main: logger", "system.process.cpu.percent": 0, "system.process.handles": 61, "system.process.id": 915585, "system.process.memory.used.bytes": 4444160, "system.process.memory.used.percent": 0, "system.process.name": "postgres", "system.process.started.time": " 20 days 3 hours 53 minutes 12 seconds", "system.process.started.time.seconds": 1741992, "system.process.threads": 1, "system.process.user": "postgres", "system.process.virtual.memory.bytes": 79015936}, {"status": "Up", "system.process": "postgres|postgres: 16/main: checkpointer", "system.process.command": "postgres: 16/main: checkpointer", "system.process.cpu.percent": 0, "system.process.handles": 62, "system.process.id": 915586, "system.process.memory.used.bytes": 46018560, "system.process.memory.used.percent": 0.3, "system.process.name": "postgres", "system.process.started.time": " 20 days 3 hours 53 minutes 12 seconds", "system.process.started.time.seconds": 1741992, "system.process.threads": 1, "system.process.user": "postgres", "system.process.virtual.memory.bytes": 229076992}, {"status": "Up", "system.process": "postgres|postgres: 16/main: background writer", "system.process.command": "postgres: 16/main: background writer", "system.process.cpu.percent": 0, "system.process.handles": 61, "system.process.id": 915587, "system.process.memory.used.bytes": 10760192, "system.process.memory.used.percent": 0, "system.process.name": "postgres", "system.process.started.time": " 20 days 3 hours 53 minutes 12 seconds", "system.process.started.time.seconds": 1741992, "system.process.threads": 1, "system.process.user": "postgres", "system.process.virtual.memory.bytes": 228745216}, {"status": "Down", "system.process": "sudo|sudo -u postgres psql", "system.process.command": "sudo -u postgres psql", "system.process.name": "sudo"}, {"status": "Down", "system.process": "psql|/usr/lib/postgresql/16/bin/psql", "system.process.command": "/usr/lib/postgresql/16/bin/psql", "system.process.name": "psql"}, {"status": "Down", "system.process": "postgres|postgres: 16/main: motadata motadata 127.0.0.1(53396) idle", "system.process.command": "postgres: 16/main: motadata motadata 127.0.0.1(53396) idle", "system.process.name": "postgres"}, {"status": "Down", "system.process": "postgres|postgres: 16/main: motadata motadata 127.0.0.1(52362) idle", "system.process.command": "postgres: 16/main: motadata motadata 127.0.0.1(52362) idle", "system.process.name": "postgres"}], "system.process.network.connection": [{"system.process": "postgres|/usr/lib/postgresql/16/bin/postgres -D /var/lib/postgresql/16/main -c config_file=/etc/postgresql/16/main/postgresql.conf", "system.process.destination.ip": "************", "system.process.destination.port": "*", "system.process.source.ip": "************", "system.process.source.port": "5432"}]}, {"correlation.metrics": ["system.process.network.connection"], "system.process": [{"status": "Up", "system.process": "postgres|/usr/lib/postgresql/16/bin/postgres -D /var/lib/postgresql/16/main -c config_file=/etc/postgresql/16/main/postgresql.conf", "system.process.command": "/usr/lib/postgresql/16/bin/postgres -D /var/lib/postgresql/16/main -c config_file=/etc/postgresql/16/main/postgresql.conf", "system.process.cpu.percent": 0, "system.process.handles": 66, "system.process.id": 915584, "system.process.memory.used.bytes": 18481152, "system.process.memory.used.percent": 0.1, "system.process.name": "postgres", "system.process.started.time": " 20 days 3 hours 58 minutes 12 seconds", "system.process.started.time.seconds": 1742292, "system.process.threads": 1, "system.process.user": "postgres", "system.process.virtual.memory.bytes": 228593664}, {"status": "Up", "system.process": "postgres|postgres: 16/main: logger", "system.process.command": "postgres: 16/main: logger", "system.process.cpu.percent": 0, "system.process.handles": 61, "system.process.id": 915585, "system.process.memory.used.bytes": 4444160, "system.process.memory.used.percent": 0, "system.process.name": "postgres", "system.process.started.time": " 20 days 3 hours 58 minutes 12 seconds", "system.process.started.time.seconds": 1742292, "system.process.threads": 1, "system.process.user": "postgres", "system.process.virtual.memory.bytes": 79015936}, {"status": "Up", "system.process": "postgres|postgres: 16/main: checkpointer", "system.process.command": "postgres: 16/main: checkpointer", "system.process.cpu.percent": 0, "system.process.handles": 62, "system.process.id": 915586, "system.process.memory.used.bytes": 46018560, "system.process.memory.used.percent": 0.3, "system.process.name": "postgres", "system.process.started.time": " 20 days 3 hours 58 minutes 12 seconds", "system.process.started.time.seconds": 1742292, "system.process.threads": 1, "system.process.user": "postgres", "system.process.virtual.memory.bytes": 229076992}, {"status": "Up", "system.process": "postgres|postgres: 16/main: background writer", "system.process.command": "postgres: 16/main: background writer", "system.process.cpu.percent": 0, "system.process.handles": 61, "system.process.id": 915587, "system.process.memory.used.bytes": 10760192, "system.process.memory.used.percent": 0, "system.process.name": "postgres", "system.process.started.time": " 20 days 3 hours 58 minutes 12 seconds", "system.process.started.time.seconds": 1742292, "system.process.threads": 1, "system.process.user": "postgres", "system.process.virtual.memory.bytes": 228745216}, {"status": "Up", "system.process": "postgres|postgres: 16/main: walwriter", "system.process.command": "postgres: 16/main: walwriter", "system.process.cpu.percent": 0, "system.process.handles": 62, "system.process.id": 915591, "system.process.memory.used.bytes": 8925184, "system.process.memory.used.percent": 0, "system.process.name": "postgres", "system.process.started.time": " 20 days 3 hours 58 minutes 12 seconds", "system.process.started.time.seconds": 1742292, "system.process.threads": 1, "system.process.user": "postgres", "system.process.virtual.memory.bytes": 228593664}, {"status": "Up", "system.process": "postgres|postgres: 16/main: autovacuum launcher", "system.process.command": "postgres: 16/main: autovacuum launcher", "system.process.cpu.percent": 0, "system.process.handles": 63, "system.process.id": 915592, "system.process.memory.used.bytes": 8663040, "system.process.memory.used.percent": 0, "system.process.name": "postgres", "system.process.started.time": " 20 days 3 hours 58 minutes 12 seconds", "system.process.started.time.seconds": 1742292, "system.process.threads": 1, "system.process.user": "postgres", "system.process.virtual.memory.bytes": 230232064}, {"status": "Up", "system.process": "postgres|postgres: 16/main: logical replication launcher", "system.process.command": "postgres: 16/main: logical replication launcher", "system.process.cpu.percent": 0, "system.process.handles": 63, "system.process.id": 915593, "system.process.memory.used.bytes": 5910528, "system.process.memory.used.percent": 0, "system.process.name": "postgres", "system.process.started.time": " 20 days 3 hours 58 minutes 12 seconds", "system.process.started.time.seconds": 1742292, "system.process.threads": 1, "system.process.user": "postgres", "system.process.virtual.memory.bytes": 230207488}, {"status": "Down", "system.process": "sudo|sudo -u postgres psql", "system.process.command": "sudo -u postgres psql", "system.process.name": "sudo"}, {"status": "Down", "system.process": "psql|/usr/lib/postgresql/16/bin/psql", "system.process.command": "/usr/lib/postgresql/16/bin/psql", "system.process.name": "psql"}, {"status": "Down", "system.process": "postgres|postgres: 16/main: motadata motadata 127.0.0.1(53396) idle", "system.process.command": "postgres: 16/main: motadata motadata 127.0.0.1(53396) idle", "system.process.name": "postgres"}, {"status": "Down", "system.process": "postgres|postgres: 16/main: motadata motadata 127.0.0.1(52362) idle", "system.process.command": "postgres: 16/main: motadata motadata 127.0.0.1(52362) idle", "system.process.name": "postgres"}], "system.process.network.connection": [{"system.process": "postgres|/usr/lib/postgresql/16/bin/postgres -D /var/lib/postgresql/16/main -c config_file=/etc/postgresql/16/main/postgresql.conf", "system.process.destination.ip": "************", "system.process.destination.port": "*", "system.process.source.ip": "************", "system.process.source.port": "5432"}]}, {"correlation.metrics": ["system.process.network.connection"], "system.process": [{"status": "Up", "system.process": "postgres|postgres: 16/main: logical replication launcher", "system.process.command": "postgres: 16/main: logical replication launcher", "system.process.cpu.percent": 0, "system.process.handles": 63, "system.process.id": 915593, "system.process.memory.used.bytes": 5910528, "system.process.memory.used.percent": 0, "system.process.name": "postgres", "system.process.started.time": " 20 days 4 hours 3 minutes 12 seconds", "system.process.started.time.seconds": 1742592, "system.process.threads": 1, "system.process.user": "postgres", "system.process.virtual.memory.bytes": 230207488}, {"status": "Up", "system.process": "postgres|/usr/lib/postgresql/16/bin/postgres -D /var/lib/postgresql/16/main -c config_file=/etc/postgresql/16/main/postgresql.conf", "system.process.command": "/usr/lib/postgresql/16/bin/postgres -D /var/lib/postgresql/16/main -c config_file=/etc/postgresql/16/main/postgresql.conf", "system.process.cpu.percent": 0, "system.process.handles": 66, "system.process.id": 915584, "system.process.memory.used.bytes": 18481152, "system.process.memory.used.percent": 0.1, "system.process.name": "postgres", "system.process.started.time": " 20 days 4 hours 3 minutes 12 seconds", "system.process.started.time.seconds": 1742592, "system.process.threads": 1, "system.process.user": "postgres", "system.process.virtual.memory.bytes": 228593664}, {"status": "Up", "system.process": "postgres|postgres: 16/main: logger", "system.process.command": "postgres: 16/main: logger", "system.process.cpu.percent": 0, "system.process.handles": 61, "system.process.id": 915585, "system.process.memory.used.bytes": 4444160, "system.process.memory.used.percent": 0, "system.process.name": "postgres", "system.process.started.time": " 20 days 4 hours 3 minutes 12 seconds", "system.process.started.time.seconds": 1742592, "system.process.threads": 1, "system.process.user": "postgres", "system.process.virtual.memory.bytes": 79015936}, {"status": "Up", "system.process": "postgres|postgres: 16/main: checkpointer", "system.process.command": "postgres: 16/main: checkpointer", "system.process.cpu.percent": 0, "system.process.handles": 62, "system.process.id": 915586, "system.process.memory.used.bytes": 46018560, "system.process.memory.used.percent": 0.3, "system.process.name": "postgres", "system.process.started.time": " 20 days 4 hours 3 minutes 12 seconds", "system.process.started.time.seconds": 1742592, "system.process.threads": 1, "system.process.user": "postgres", "system.process.virtual.memory.bytes": 229076992}, {"status": "Up", "system.process": "postgres|postgres: 16/main: background writer", "system.process.command": "postgres: 16/main: background writer", "system.process.cpu.percent": 0, "system.process.handles": 61, "system.process.id": 915587, "system.process.memory.used.bytes": 10760192, "system.process.memory.used.percent": 0, "system.process.name": "postgres", "system.process.started.time": " 20 days 4 hours 3 minutes 12 seconds", "system.process.started.time.seconds": 1742592, "system.process.threads": 1, "system.process.user": "postgres", "system.process.virtual.memory.bytes": 228745216}, {"status": "Up", "system.process": "postgres|postgres: 16/main: walwriter", "system.process.command": "postgres: 16/main: walwriter", "system.process.cpu.percent": 0, "system.process.handles": 62, "system.process.id": 915591, "system.process.memory.used.bytes": 8925184, "system.process.memory.used.percent": 0, "system.process.name": "postgres", "system.process.started.time": " 20 days 4 hours 3 minutes 12 seconds", "system.process.started.time.seconds": 1742592, "system.process.threads": 1, "system.process.user": "postgres", "system.process.virtual.memory.bytes": 228593664}, {"status": "Up", "system.process": "postgres|postgres: 16/main: autovacuum launcher", "system.process.command": "postgres: 16/main: autovacuum launcher", "system.process.cpu.percent": 0, "system.process.handles": 63, "system.process.id": 915592, "system.process.memory.used.bytes": 8663040, "system.process.memory.used.percent": 0, "system.process.name": "postgres", "system.process.started.time": " 20 days 4 hours 3 minutes 12 seconds", "system.process.started.time.seconds": 1742592, "system.process.threads": 1, "system.process.user": "postgres", "system.process.virtual.memory.bytes": 230232064}, {"status": "Down", "system.process": "sudo|sudo -u postgres psql", "system.process.command": "sudo -u postgres psql", "system.process.name": "sudo"}, {"status": "Down", "system.process": "psql|/usr/lib/postgresql/16/bin/psql", "system.process.command": "/usr/lib/postgresql/16/bin/psql", "system.process.name": "psql"}, {"status": "Down", "system.process": "postgres|postgres: 16/main: motadata motadata 127.0.0.1(53396) idle", "system.process.command": "postgres: 16/main: motadata motadata 127.0.0.1(53396) idle", "system.process.name": "postgres"}, {"status": "Down", "system.process": "postgres|postgres: 16/main: motadata motadata 127.0.0.1(52362) idle", "system.process.command": "postgres: 16/main: motadata motadata 127.0.0.1(52362) idle", "system.process.name": "postgres"}], "system.process.network.connection": [{"system.process": "postgres|/usr/lib/postgresql/16/bin/postgres -D /var/lib/postgresql/16/main -c config_file=/etc/postgresql/16/main/postgresql.conf", "system.process.destination.ip": "************", "system.process.destination.port": "*", "system.process.source.ip": "************", "system.process.source.port": "5432"}]}, {"correlation.metrics": ["system.process.network.connection"], "system.process": [{"status": "Up", "system.process": "postgres|postgres: 16/main: background writer", "system.process.command": "postgres: 16/main: background writer", "system.process.cpu.percent": 0, "system.process.handles": 61, "system.process.id": 915587, "system.process.memory.used.bytes": 10760192, "system.process.memory.used.percent": 0, "system.process.name": "postgres", "system.process.started.time": " 20 days 4 hours 8 minutes 12 seconds", "system.process.started.time.seconds": 1742892, "system.process.threads": 1, "system.process.user": "postgres", "system.process.virtual.memory.bytes": 228745216}, {"status": "Up", "system.process": "postgres|postgres: 16/main: walwriter", "system.process.command": "postgres: 16/main: walwriter", "system.process.cpu.percent": 0, "system.process.handles": 62, "system.process.id": 915591, "system.process.memory.used.bytes": 8925184, "system.process.memory.used.percent": 0, "system.process.name": "postgres", "system.process.started.time": " 20 days 4 hours 8 minutes 12 seconds", "system.process.started.time.seconds": 1742892, "system.process.threads": 1, "system.process.user": "postgres", "system.process.virtual.memory.bytes": 228593664}, {"status": "Up", "system.process": "postgres|postgres: 16/main: autovacuum launcher", "system.process.command": "postgres: 16/main: autovacuum launcher", "system.process.cpu.percent": 0, "system.process.handles": 63, "system.process.id": 915592, "system.process.memory.used.bytes": 8663040, "system.process.memory.used.percent": 0, "system.process.name": "postgres", "system.process.started.time": " 20 days 4 hours 8 minutes 12 seconds", "system.process.started.time.seconds": 1742892, "system.process.threads": 1, "system.process.user": "postgres", "system.process.virtual.memory.bytes": 230232064}, {"status": "Up", "system.process": "postgres|postgres: 16/main: logical replication launcher", "system.process.command": "postgres: 16/main: logical replication launcher", "system.process.cpu.percent": 0, "system.process.handles": 63, "system.process.id": 915593, "system.process.memory.used.bytes": 5910528, "system.process.memory.used.percent": 0, "system.process.name": "postgres", "system.process.started.time": " 20 days 4 hours 8 minutes 12 seconds", "system.process.started.time.seconds": 1742892, "system.process.threads": 1, "system.process.user": "postgres", "system.process.virtual.memory.bytes": 230207488}, {"status": "Up", "system.process": "postgres|/usr/lib/postgresql/16/bin/postgres -D /var/lib/postgresql/16/main -c config_file=/etc/postgresql/16/main/postgresql.conf", "system.process.command": "/usr/lib/postgresql/16/bin/postgres -D /var/lib/postgresql/16/main -c config_file=/etc/postgresql/16/main/postgresql.conf", "system.process.cpu.percent": 0, "system.process.handles": 66, "system.process.id": 915584, "system.process.memory.used.bytes": 18481152, "system.process.memory.used.percent": 0.1, "system.process.name": "postgres", "system.process.started.time": " 20 days 4 hours 8 minutes 12 seconds", "system.process.started.time.seconds": 1742892, "system.process.threads": 1, "system.process.user": "postgres", "system.process.virtual.memory.bytes": 228593664}, {"status": "Up", "system.process": "postgres|postgres: 16/main: logger", "system.process.command": "postgres: 16/main: logger", "system.process.cpu.percent": 0, "system.process.handles": 61, "system.process.id": 915585, "system.process.memory.used.bytes": 4444160, "system.process.memory.used.percent": 0, "system.process.name": "postgres", "system.process.started.time": " 20 days 4 hours 8 minutes 12 seconds", "system.process.started.time.seconds": 1742892, "system.process.threads": 1, "system.process.user": "postgres", "system.process.virtual.memory.bytes": 79015936}, {"status": "Up", "system.process": "postgres|postgres: 16/main: checkpointer", "system.process.command": "postgres: 16/main: checkpointer", "system.process.cpu.percent": 0, "system.process.handles": 62, "system.process.id": 915586, "system.process.memory.used.bytes": 46018560, "system.process.memory.used.percent": 0.3, "system.process.name": "postgres", "system.process.started.time": " 20 days 4 hours 8 minutes 12 seconds", "system.process.started.time.seconds": 1742892, "system.process.threads": 1, "system.process.user": "postgres", "system.process.virtual.memory.bytes": 229076992}, {"status": "Down", "system.process": "sudo|sudo -u postgres psql", "system.process.command": "sudo -u postgres psql", "system.process.name": "sudo"}, {"status": "Down", "system.process": "psql|/usr/lib/postgresql/16/bin/psql", "system.process.command": "/usr/lib/postgresql/16/bin/psql", "system.process.name": "psql"}, {"status": "Down", "system.process": "postgres|postgres: 16/main: motadata motadata 127.0.0.1(53396) idle", "system.process.command": "postgres: 16/main: motadata motadata 127.0.0.1(53396) idle", "system.process.name": "postgres"}, {"status": "Down", "system.process": "postgres|postgres: 16/main: motadata motadata 127.0.0.1(52362) idle", "system.process.command": "postgres: 16/main: motadata motadata 127.0.0.1(52362) idle", "system.process.name": "postgres"}], "system.process.network.connection": [{"system.process": "postgres|/usr/lib/postgresql/16/bin/postgres -D /var/lib/postgresql/16/main -c config_file=/etc/postgresql/16/main/postgresql.conf", "system.process.destination.ip": "************", "system.process.destination.port": "*", "system.process.source.ip": "************", "system.process.source.port": "5432"}]}, {"correlation.metrics": ["system.process.network.connection"], "system.process": [{"status": "Up", "system.process": "postgres|/usr/lib/postgresql/16/bin/postgres -D /var/lib/postgresql/16/main -c config_file=/etc/postgresql/16/main/postgresql.conf", "system.process.command": "/usr/lib/postgresql/16/bin/postgres -D /var/lib/postgresql/16/main -c config_file=/etc/postgresql/16/main/postgresql.conf", "system.process.cpu.percent": 0, "system.process.handles": 66, "system.process.id": 915584, "system.process.memory.used.bytes": 18481152, "system.process.memory.used.percent": 0.1, "system.process.name": "postgres", "system.process.started.time": " 20 days 4 hours 13 minutes 14 seconds", "system.process.started.time.seconds": 1743194, "system.process.threads": 1, "system.process.user": "postgres", "system.process.virtual.memory.bytes": 228593664}, {"status": "Up", "system.process": "postgres|postgres: 16/main: logger", "system.process.command": "postgres: 16/main: logger", "system.process.cpu.percent": 0, "system.process.handles": 61, "system.process.id": 915585, "system.process.memory.used.bytes": 4444160, "system.process.memory.used.percent": 0, "system.process.name": "postgres", "system.process.started.time": " 20 days 4 hours 13 minutes 14 seconds", "system.process.started.time.seconds": 1743194, "system.process.threads": 1, "system.process.user": "postgres", "system.process.virtual.memory.bytes": 79015936}, {"status": "Up", "system.process": "postgres|postgres: 16/main: checkpointer", "system.process.command": "postgres: 16/main: checkpointer", "system.process.cpu.percent": 0, "system.process.handles": 62, "system.process.id": 915586, "system.process.memory.used.bytes": 46018560, "system.process.memory.used.percent": 0.3, "system.process.name": "postgres", "system.process.started.time": " 20 days 4 hours 13 minutes 14 seconds", "system.process.started.time.seconds": 1743194, "system.process.threads": 1, "system.process.user": "postgres", "system.process.virtual.memory.bytes": 229076992}, {"status": "Up", "system.process": "postgres|postgres: 16/main: background writer", "system.process.command": "postgres: 16/main: background writer", "system.process.cpu.percent": 0, "system.process.handles": 61, "system.process.id": 915587, "system.process.memory.used.bytes": 10760192, "system.process.memory.used.percent": 0, "system.process.name": "postgres", "system.process.started.time": " 20 days 4 hours 13 minutes 14 seconds", "system.process.started.time.seconds": 1743194, "system.process.threads": 1, "system.process.user": "postgres", "system.process.virtual.memory.bytes": 228745216}, {"status": "Up", "system.process": "postgres|postgres: 16/main: walwriter", "system.process.command": "postgres: 16/main: walwriter", "system.process.cpu.percent": 0, "system.process.handles": 62, "system.process.id": 915591, "system.process.memory.used.bytes": 8925184, "system.process.memory.used.percent": 0, "system.process.name": "postgres", "system.process.started.time": " 20 days 4 hours 13 minutes 14 seconds", "system.process.started.time.seconds": 1743194, "system.process.threads": 1, "system.process.user": "postgres", "system.process.virtual.memory.bytes": 228593664}, {"status": "Up", "system.process": "postgres|postgres: 16/main: autovacuum launcher", "system.process.command": "postgres: 16/main: autovacuum launcher", "system.process.cpu.percent": 0, "system.process.handles": 63, "system.process.id": 915592, "system.process.memory.used.bytes": 8663040, "system.process.memory.used.percent": 0, "system.process.name": "postgres", "system.process.started.time": " 20 days 4 hours 13 minutes 14 seconds", "system.process.started.time.seconds": 1743194, "system.process.threads": 1, "system.process.user": "postgres", "system.process.virtual.memory.bytes": 230232064}, {"status": "Up", "system.process": "postgres|postgres: 16/main: logical replication launcher", "system.process.command": "postgres: 16/main: logical replication launcher", "system.process.cpu.percent": 0, "system.process.handles": 63, "system.process.id": 915593, "system.process.memory.used.bytes": 5910528, "system.process.memory.used.percent": 0, "system.process.name": "postgres", "system.process.started.time": " 20 days 4 hours 13 minutes 14 seconds", "system.process.started.time.seconds": 1743194, "system.process.threads": 1, "system.process.user": "postgres", "system.process.virtual.memory.bytes": 230207488}, {"status": "Down", "system.process": "sudo|sudo -u postgres psql", "system.process.command": "sudo -u postgres psql", "system.process.name": "sudo"}, {"status": "Down", "system.process": "psql|/usr/lib/postgresql/16/bin/psql", "system.process.command": "/usr/lib/postgresql/16/bin/psql", "system.process.name": "psql"}, {"status": "Down", "system.process": "postgres|postgres: 16/main: motadata motadata 127.0.0.1(53396) idle", "system.process.command": "postgres: 16/main: motadata motadata 127.0.0.1(53396) idle", "system.process.name": "postgres"}, {"status": "Down", "system.process": "postgres|postgres: 16/main: motadata motadata 127.0.0.1(52362) idle", "system.process.command": "postgres: 16/main: motadata motadata 127.0.0.1(52362) idle", "system.process.name": "postgres"}], "system.process.network.connection": [{"system.process": "postgres|/usr/lib/postgresql/16/bin/postgres -D /var/lib/postgresql/16/main -c config_file=/etc/postgresql/16/main/postgresql.conf", "system.process.destination.ip": "************", "system.process.destination.port": "*", "system.process.source.ip": "************", "system.process.source.port": "5432"}]}, {"correlation.metrics": ["system.process.network.connection"], "system.process": [{"status": "Up", "system.process": "postgres|postgres: 16/main: logger", "system.process.command": "postgres: 16/main: logger", "system.process.cpu.percent": 0, "system.process.handles": 61, "system.process.id": 915585, "system.process.memory.used.bytes": 4444160, "system.process.memory.used.percent": 0, "system.process.name": "postgres", "system.process.started.time": " 20 days 4 hours 23 minutes 13 seconds", "system.process.started.time.seconds": 1743793, "system.process.threads": 1, "system.process.user": "postgres", "system.process.virtual.memory.bytes": 79015936}, {"status": "Up", "system.process": "postgres|postgres: 16/main: checkpointer", "system.process.command": "postgres: 16/main: checkpointer", "system.process.cpu.percent": 0, "system.process.handles": 62, "system.process.id": 915586, "system.process.memory.used.bytes": 46018560, "system.process.memory.used.percent": 0.3, "system.process.name": "postgres", "system.process.started.time": " 20 days 4 hours 23 minutes 13 seconds", "system.process.started.time.seconds": 1743793, "system.process.threads": 1, "system.process.user": "postgres", "system.process.virtual.memory.bytes": 229076992}, {"status": "Up", "system.process": "postgres|postgres: 16/main: background writer", "system.process.command": "postgres: 16/main: background writer", "system.process.cpu.percent": 0, "system.process.handles": 61, "system.process.id": 915587, "system.process.memory.used.bytes": 10760192, "system.process.memory.used.percent": 0, "system.process.name": "postgres", "system.process.started.time": " 20 days 4 hours 23 minutes 13 seconds", "system.process.started.time.seconds": 1743793, "system.process.threads": 1, "system.process.user": "postgres", "system.process.virtual.memory.bytes": 228745216}, {"status": "Up", "system.process": "postgres|postgres: 16/main: walwriter", "system.process.command": "postgres: 16/main: walwriter", "system.process.cpu.percent": 0, "system.process.handles": 62, "system.process.id": 915591, "system.process.memory.used.bytes": 8925184, "system.process.memory.used.percent": 0, "system.process.name": "postgres", "system.process.started.time": " 20 days 4 hours 23 minutes 13 seconds", "system.process.started.time.seconds": 1743793, "system.process.threads": 1, "system.process.user": "postgres", "system.process.virtual.memory.bytes": 228593664}, {"status": "Up", "system.process": "postgres|postgres: 16/main: autovacuum launcher", "system.process.command": "postgres: 16/main: autovacuum launcher", "system.process.cpu.percent": 0, "system.process.handles": 63, "system.process.id": 915592, "system.process.memory.used.bytes": 8663040, "system.process.memory.used.percent": 0, "system.process.name": "postgres", "system.process.started.time": " 20 days 4 hours 23 minutes 13 seconds", "system.process.started.time.seconds": 1743793, "system.process.threads": 1, "system.process.user": "postgres", "system.process.virtual.memory.bytes": 230232064}, {"status": "Up", "system.process": "postgres|postgres: 16/main: logical replication launcher", "system.process.command": "postgres: 16/main: logical replication launcher", "system.process.cpu.percent": 0, "system.process.handles": 63, "system.process.id": 915593, "system.process.memory.used.bytes": 5910528, "system.process.memory.used.percent": 0, "system.process.name": "postgres", "system.process.started.time": " 20 days 4 hours 23 minutes 13 seconds", "system.process.started.time.seconds": 1743793, "system.process.threads": 1, "system.process.user": "postgres", "system.process.virtual.memory.bytes": 230207488}, {"status": "Up", "system.process": "postgres|/usr/lib/postgresql/16/bin/postgres -D /var/lib/postgresql/16/main -c config_file=/etc/postgresql/16/main/postgresql.conf", "system.process.command": "/usr/lib/postgresql/16/bin/postgres -D /var/lib/postgresql/16/main -c config_file=/etc/postgresql/16/main/postgresql.conf", "system.process.cpu.percent": 0, "system.process.handles": 66, "system.process.id": 915584, "system.process.memory.used.bytes": 18481152, "system.process.memory.used.percent": 0.1, "system.process.name": "postgres", "system.process.started.time": " 20 days 4 hours 23 minutes 13 seconds", "system.process.started.time.seconds": 1743793, "system.process.threads": 1, "system.process.user": "postgres", "system.process.virtual.memory.bytes": 228593664}, {"status": "Down", "system.process": "sudo|sudo -u postgres psql", "system.process.command": "sudo -u postgres psql", "system.process.name": "sudo"}, {"status": "Down", "system.process": "psql|/usr/lib/postgresql/16/bin/psql", "system.process.command": "/usr/lib/postgresql/16/bin/psql", "system.process.name": "psql"}, {"status": "Down", "system.process": "postgres|postgres: 16/main: motadata motadata 127.0.0.1(53396) idle", "system.process.command": "postgres: 16/main: motadata motadata 127.0.0.1(53396) idle", "system.process.name": "postgres"}, {"status": "Down", "system.process": "postgres|postgres: 16/main: motadata motadata 127.0.0.1(52362) idle", "system.process.command": "postgres: 16/main: motadata motadata 127.0.0.1(52362) idle", "system.process.name": "postgres"}], "system.process.network.connection": [{"system.process": "postgres|/usr/lib/postgresql/16/bin/postgres -D /var/lib/postgresql/16/main -c config_file=/etc/postgresql/16/main/postgresql.conf", "system.process.destination.ip": "************", "system.process.destination.port": "*", "system.process.source.ip": "************", "system.process.source.port": "5432"}]}], "rediscovery": null}, "***********": {"discovery": null, "collect": [{"correlation.metrics": ["system.process.network.connection"], "system.process": [{"status": "Up", "system.process": "vserver|/opt/sdb/MaxDB/pgm/vserver -sdbstarter 1001 1001 A -S 7200 -Y", "system.process.command": "/opt/sdb/MaxDB/pgm/vserver -sdbstarter 1001 1001 A -S 7200 -Y", "system.process.cpu.percent": 0, "system.process.handles": 0, "system.process.id": 1695, "system.process.memory.used.bytes": 167936, "system.process.memory.used.percent": 0, "system.process.name": "vserver", "system.process.started.time": " 141 days 22 hours 28 minutes 27 seconds", "system.process.started.time.seconds": 12263307, "system.process.threads": 1, "system.process.user": "sdb", "system.process.virtual.memory.bytes": 32313344}, {"status": "Up", "system.process": "vserver|sdbgloballistener start -m", "system.process.command": "sdbgloballistener start -m", "system.process.cpu.percent": 0, "system.process.handles": 0, "system.process.id": 1701, "system.process.memory.used.bytes": 3387392, "system.process.memory.used.percent": 0.1, "system.process.name": "vserver", "system.process.started.time": " 141 days 22 hours 28 minutes 27 seconds", "system.process.started.time.seconds": 12263307, "system.process.threads": 1, "system.process.user": "sdb", "system.process.virtual.memory.bytes": 36646912}, {"status": "Up", "system.process": "vserver|sdbgloballistener start -m", "system.process.command": "sdbgloballistener start -m", "system.process.cpu.percent": 0, "system.process.handles": 0, "system.process.id": 1702, "system.process.memory.used.bytes": 167936, "system.process.memory.used.percent": 0, "system.process.name": "vserver", "system.process.started.time": " 141 days 22 hours 28 minutes 27 seconds", "system.process.started.time.seconds": 12263307, "system.process.threads": 1, "system.process.user": "sdb", "system.process.virtual.memory.bytes": 32313344}, {"status": "Up", "system.process": "vserver|sdbgloballistener start -m", "system.process.command": "sdbgloballistener start -m", "system.process.cpu.percent": 0, "system.process.id": 8213, "system.process.memory.used.bytes": 2658304, "system.process.memory.used.percent": 0.1, "system.process.name": "vserver", "system.process.started.time": " 56 days 16 hours 10 minutes 50 seconds", "system.process.started.time.seconds": 4896650, "system.process.threads": 1, "system.process.user": "sdb", "system.process.virtual.memory.bytes": 36802560}, {"status": "Up", "system.process": "vserver|/opt/sdb/MaxDB/pgm/vserver -sdbstarter 1001 1001 A -S 7200 -Y", "system.process.command": "/opt/sdb/MaxDB/pgm/vserver -sdbstarter 1001 1001 A -S 7200 -Y", "system.process.cpu.percent": 0, "system.process.handles": 0, "system.process.id": 8688, "system.process.memory.used.bytes": 2682880, "system.process.memory.used.percent": 0.1, "system.process.name": "vserver", "system.process.started.time": " 56 days 16 hours 7 minutes 17 seconds", "system.process.started.time.seconds": 4896437, "system.process.threads": 1, "system.process.user": "sdb", "system.process.virtual.memory.bytes": 37703680}, {"status": "Up", "system.process": "vserver|/opt/sdb/MaxDB/pgm/vserver -sdbstarter 1001 1001 A -S 7200 -Y", "system.process.command": "/opt/sdb/MaxDB/pgm/vserver -sdbstarter 1001 1001 A -S 7200 -Y", "system.process.cpu.percent": 0, "system.process.handles": 0, "system.process.id": 8796, "system.process.memory.used.bytes": 2682880, "system.process.memory.used.percent": 0.1, "system.process.name": "vserver", "system.process.started.time": " 56 days 16 hours 7 minutes 3 seconds", "system.process.started.time.seconds": 4896423, "system.process.threads": 1, "system.process.user": "sdb", "system.process.virtual.memory.bytes": 37703680}, {"status": "Up", "system.process": "vserver|/opt/sdb/MaxDB/pgm/vserver -sdbstarter 1001 1001 A -S 7200 -Y", "system.process.command": "/opt/sdb/MaxDB/pgm/vserver -sdbstarter 1001 1001 A -S 7200 -Y", "system.process.cpu.percent": 0, "system.process.id": 1694, "system.process.memory.used.bytes": 3403776, "system.process.memory.used.percent": 0.1, "system.process.name": "vserver", "system.process.started.time": " 141 days 22 hours 28 minutes 27 seconds", "system.process.started.time.seconds": 12263307, "system.process.threads": 1, "system.process.user": "sdb", "system.process.virtual.memory.bytes": 36646912}]}, {"correlation.metrics": ["system.process.network.connection"], "system.process": [{"status": "Up", "system.process": "vserver|/opt/sdb/MaxDB/pgm/vserver -sdbstarter 1001 1001 A -S 7200 -Y", "system.process.command": "/opt/sdb/MaxDB/pgm/vserver -sdbstarter 1001 1001 A -S 7200 -Y", "system.process.cpu.percent": 0, "system.process.handles": 0, "system.process.id": 1694, "system.process.memory.used.bytes": 3403776, "system.process.memory.used.percent": 0.1, "system.process.name": "vserver", "system.process.started.time": " 141 days 22 hours 30 minutes 30 seconds", "system.process.started.time.seconds": 12263430, "system.process.threads": 1, "system.process.user": "sdb", "system.process.virtual.memory.bytes": 36646912}, {"status": "Up", "system.process": "vserver|/opt/sdb/MaxDB/pgm/vserver -sdbstarter 1001 1001 A -S 7200 -Y", "system.process.command": "/opt/sdb/MaxDB/pgm/vserver -sdbstarter 1001 1001 A -S 7200 -Y", "system.process.cpu.percent": 0, "system.process.handles": 0, "system.process.id": 1695, "system.process.memory.used.bytes": 167936, "system.process.memory.used.percent": 0, "system.process.name": "vserver", "system.process.started.time": " 141 days 22 hours 30 minutes 30 seconds", "system.process.started.time.seconds": 12263430, "system.process.threads": 1, "system.process.user": "sdb", "system.process.virtual.memory.bytes": 32313344}, {"status": "Up", "system.process": "vserver|sdbgloballistener start -m", "system.process.command": "sdbgloballistener start -m", "system.process.cpu.percent": 0, "system.process.handles": 0, "system.process.id": 1701, "system.process.memory.used.bytes": 3387392, "system.process.memory.used.percent": 0.1, "system.process.name": "vserver", "system.process.started.time": " 141 days 22 hours 30 minutes 30 seconds", "system.process.started.time.seconds": 12263430, "system.process.threads": 1, "system.process.user": "sdb", "system.process.virtual.memory.bytes": 36646912}, {"status": "Up", "system.process": "vserver|sdbgloballistener start -m", "system.process.command": "sdbgloballistener start -m", "system.process.cpu.percent": 0, "system.process.handles": 0, "system.process.id": 1702, "system.process.memory.used.bytes": 167936, "system.process.memory.used.percent": 0, "system.process.name": "vserver", "system.process.started.time": " 141 days 22 hours 30 minutes 30 seconds", "system.process.started.time.seconds": 12263430, "system.process.threads": 1, "system.process.user": "sdb", "system.process.virtual.memory.bytes": 32313344}, {"status": "Up", "system.process": "vserver|sdbgloballistener start -m", "system.process.command": "sdbgloballistener start -m", "system.process.cpu.percent": 0, "system.process.handles": 0, "system.process.id": 8213, "system.process.memory.used.bytes": 2658304, "system.process.memory.used.percent": 0.1, "system.process.name": "vserver", "system.process.started.time": " 56 days 16 hours 12 minutes 53 seconds", "system.process.started.time.seconds": 4896773, "system.process.threads": 1, "system.process.user": "sdb", "system.process.virtual.memory.bytes": 36802560}, {"status": "Up", "system.process": "vserver|/opt/sdb/MaxDB/pgm/vserver -sdbstarter 1001 1001 A -S 7200 -Y", "system.process.command": "/opt/sdb/MaxDB/pgm/vserver -sdbstarter 1001 1001 A -S 7200 -Y", "system.process.cpu.percent": 0, "system.process.handles": 0, "system.process.id": 8688, "system.process.memory.used.bytes": 2682880, "system.process.memory.used.percent": 0.1, "system.process.name": "vserver", "system.process.started.time": " 56 days 16 hours 9 minutes 20 seconds", "system.process.started.time.seconds": 4896560, "system.process.threads": 1, "system.process.user": "sdb", "system.process.virtual.memory.bytes": 37703680}, {"status": "Up", "system.process": "vserver|/opt/sdb/MaxDB/pgm/vserver -sdbstarter 1001 1001 A -S 7200 -Y", "system.process.command": "/opt/sdb/MaxDB/pgm/vserver -sdbstarter 1001 1001 A -S 7200 -Y", "system.process.cpu.percent": 0, "system.process.handles": 0, "system.process.id": 8796, "system.process.memory.used.bytes": 2682880, "system.process.memory.used.percent": 0.1, "system.process.name": "vserver", "system.process.started.time": " 56 days 16 hours 9 minutes 6 seconds", "system.process.started.time.seconds": 4896546, "system.process.threads": 1, "system.process.user": "sdb", "system.process.virtual.memory.bytes": 37703680}]}, {"correlation.metrics": ["system.process.network.connection"], "system.process": [{"status": "Up", "system.process": "vserver|sdbgloballistener start -m", "system.process.command": "sdbgloballistener start -m", "system.process.cpu.percent": 0, "system.process.handles": 0, "system.process.id": 1701, "system.process.memory.used.bytes": 3387392, "system.process.memory.used.percent": 0.1, "system.process.name": "vserver", "system.process.started.time": " 141 days 22 hours 35 minutes 30 seconds", "system.process.started.time.seconds": 12263730, "system.process.threads": 1, "system.process.user": "sdb", "system.process.virtual.memory.bytes": 36646912}, {"status": "Up", "system.process": "vserver|sdbgloballistener start -m", "system.process.command": "sdbgloballistener start -m", "system.process.cpu.percent": 0, "system.process.handles": 0, "system.process.id": 1702, "system.process.memory.used.bytes": 167936, "system.process.memory.used.percent": 0, "system.process.name": "vserver", "system.process.started.time": " 141 days 22 hours 35 minutes 30 seconds", "system.process.started.time.seconds": 12263730, "system.process.threads": 1, "system.process.user": "sdb", "system.process.virtual.memory.bytes": 32313344}, {"status": "Up", "system.process": "vserver|sdbgloballistener start -m", "system.process.command": "sdbgloballistener start -m", "system.process.cpu.percent": 0, "system.process.handles": 0, "system.process.id": 8213, "system.process.memory.used.bytes": 2658304, "system.process.memory.used.percent": 0.1, "system.process.name": "vserver", "system.process.started.time": " 56 days 16 hours 17 minutes 53 seconds", "system.process.started.time.seconds": 4897073, "system.process.threads": 1, "system.process.user": "sdb", "system.process.virtual.memory.bytes": 36802560}, {"status": "Up", "system.process": "vserver|/opt/sdb/MaxDB/pgm/vserver -sdbstarter 1001 1001 A -S 7200 -Y", "system.process.command": "/opt/sdb/MaxDB/pgm/vserver -sdbstarter 1001 1001 A -S 7200 -Y", "system.process.cpu.percent": 0, "system.process.handles": 0, "system.process.id": 8688, "system.process.memory.used.bytes": 2682880, "system.process.memory.used.percent": 0.1, "system.process.name": "vserver", "system.process.started.time": " 56 days 16 hours 14 minutes 20 seconds", "system.process.started.time.seconds": 4896860, "system.process.threads": 1, "system.process.user": "sdb", "system.process.virtual.memory.bytes": 37703680}, {"status": "Up", "system.process": "vserver|/opt/sdb/MaxDB/pgm/vserver -sdbstarter 1001 1001 A -S 7200 -Y", "system.process.command": "/opt/sdb/MaxDB/pgm/vserver -sdbstarter 1001 1001 A -S 7200 -Y", "system.process.cpu.percent": 0, "system.process.handles": 0, "system.process.id": 8796, "system.process.memory.used.bytes": 2682880, "system.process.memory.used.percent": 0.1, "system.process.name": "vserver", "system.process.started.time": " 56 days 16 hours 14 minutes 6 seconds", "system.process.started.time.seconds": 4896846, "system.process.threads": 1, "system.process.user": "sdb", "system.process.virtual.memory.bytes": 37703680}, {"status": "Up", "system.process": "vserver|/opt/sdb/MaxDB/pgm/vserver -sdbstarter 1001 1001 A -S 7200 -Y", "system.process.command": "/opt/sdb/MaxDB/pgm/vserver -sdbstarter 1001 1001 A -S 7200 -Y", "system.process.cpu.percent": 0, "system.process.handles": 0, "system.process.id": 1694, "system.process.memory.used.bytes": 3403776, "system.process.memory.used.percent": 0.1, "system.process.name": "vserver", "system.process.started.time": " 141 days 22 hours 35 minutes 30 seconds", "system.process.started.time.seconds": 12263730, "system.process.threads": 1, "system.process.user": "sdb", "system.process.virtual.memory.bytes": 36646912}, {"status": "Up", "system.process": "vserver|/opt/sdb/MaxDB/pgm/vserver -sdbstarter 1001 1001 A -S 7200 -Y", "system.process.command": "/opt/sdb/MaxDB/pgm/vserver -sdbstarter 1001 1001 A -S 7200 -Y", "system.process.cpu.percent": 0, "system.process.handles": 0, "system.process.id": 1695, "system.process.memory.used.bytes": 167936, "system.process.memory.used.percent": 0, "system.process.name": "vserver", "system.process.started.time": " 141 days 22 hours 35 minutes 30 seconds", "system.process.started.time.seconds": 12263730, "system.process.threads": 1, "system.process.user": "sdb", "system.process.virtual.memory.bytes": 32313344}]}, {"correlation.metrics": ["system.process.network.connection"], "system.process": [{"status": "Up", "system.process": "vserver|/opt/sdb/MaxDB/pgm/vserver -sdbstarter 1001 1001 A -S 7200 -Y", "system.process.command": "/opt/sdb/MaxDB/pgm/vserver -sdbstarter 1001 1001 A -S 7200 -Y", "system.process.cpu.percent": 0, "system.process.handles": 0, "system.process.id": 1694, "system.process.memory.used.bytes": 3403776, "system.process.memory.used.percent": 0.1, "system.process.name": "vserver", "system.process.started.time": " 141 days 22 hours 40 minutes 30 seconds", "system.process.started.time.seconds": 12264030, "system.process.threads": 1, "system.process.user": "sdb", "system.process.virtual.memory.bytes": 36646912}, {"status": "Up", "system.process": "vserver|/opt/sdb/MaxDB/pgm/vserver -sdbstarter 1001 1001 A -S 7200 -Y", "system.process.command": "/opt/sdb/MaxDB/pgm/vserver -sdbstarter 1001 1001 A -S 7200 -Y", "system.process.cpu.percent": 0, "system.process.handles": 0, "system.process.id": 1695, "system.process.memory.used.bytes": 167936, "system.process.memory.used.percent": 0, "system.process.name": "vserver", "system.process.started.time": " 141 days 22 hours 40 minutes 30 seconds", "system.process.started.time.seconds": 12264030, "system.process.threads": 1, "system.process.user": "sdb", "system.process.virtual.memory.bytes": 32313344}, {"status": "Up", "system.process": "vserver|sdbgloballistener start -m", "system.process.command": "sdbgloballistener start -m", "system.process.cpu.percent": 0, "system.process.handles": 0, "system.process.id": 1701, "system.process.memory.used.bytes": 3387392, "system.process.memory.used.percent": 0.1, "system.process.name": "vserver", "system.process.started.time": " 141 days 22 hours 40 minutes 30 seconds", "system.process.started.time.seconds": 12264030, "system.process.threads": 1, "system.process.user": "sdb", "system.process.virtual.memory.bytes": 36646912}, {"status": "Up", "system.process": "vserver|sdbgloballistener start -m", "system.process.command": "sdbgloballistener start -m", "system.process.cpu.percent": 0, "system.process.handles": 0, "system.process.id": 1702, "system.process.memory.used.bytes": 167936, "system.process.memory.used.percent": 0, "system.process.name": "vserver", "system.process.started.time": " 141 days 22 hours 40 minutes 30 seconds", "system.process.started.time.seconds": 12264030, "system.process.threads": 1, "system.process.user": "sdb", "system.process.virtual.memory.bytes": 32313344}, {"status": "Up", "system.process": "vserver|sdbgloballistener start -m", "system.process.command": "sdbgloballistener start -m", "system.process.cpu.percent": 0, "system.process.id": 8213, "system.process.memory.used.bytes": 2658304, "system.process.memory.used.percent": 0.1, "system.process.name": "vserver", "system.process.started.time": " 56 days 16 hours 22 minutes 53 seconds", "system.process.started.time.seconds": 4897373, "system.process.threads": 1, "system.process.user": "sdb", "system.process.virtual.memory.bytes": 36802560}, {"status": "Up", "system.process": "vserver|/opt/sdb/MaxDB/pgm/vserver -sdbstarter 1001 1001 A -S 7200 -Y", "system.process.command": "/opt/sdb/MaxDB/pgm/vserver -sdbstarter 1001 1001 A -S 7200 -Y", "system.process.cpu.percent": 0, "system.process.handles": 0, "system.process.id": 8688, "system.process.memory.used.bytes": 2682880, "system.process.memory.used.percent": 0.1, "system.process.name": "vserver", "system.process.started.time": " 56 days 16 hours 19 minutes 20 seconds", "system.process.started.time.seconds": 4897160, "system.process.threads": 1, "system.process.user": "sdb", "system.process.virtual.memory.bytes": 37703680}, {"status": "Up", "system.process": "vserver|/opt/sdb/MaxDB/pgm/vserver -sdbstarter 1001 1001 A -S 7200 -Y", "system.process.command": "/opt/sdb/MaxDB/pgm/vserver -sdbstarter 1001 1001 A -S 7200 -Y", "system.process.cpu.percent": 0, "system.process.handles": 0, "system.process.id": 8796, "system.process.memory.used.bytes": 2682880, "system.process.memory.used.percent": 0.1, "system.process.name": "vserver", "system.process.started.time": " 56 days 16 hours 19 minutes 6 seconds", "system.process.started.time.seconds": 4897146, "system.process.threads": 1, "system.process.user": "sdb", "system.process.virtual.memory.bytes": 37703680}]}, {"correlation.metrics": ["system.process.network.connection"], "system.process": [{"status": "Up", "system.process": "vserver|/opt/sdb/MaxDB/pgm/vserver -sdbstarter 1001 1001 A -S 7200 -Y", "system.process.command": "/opt/sdb/MaxDB/pgm/vserver -sdbstarter 1001 1001 A -S 7200 -Y", "system.process.cpu.percent": 0, "system.process.handles": 0, "system.process.id": 1695, "system.process.memory.used.bytes": 167936, "system.process.memory.used.percent": 0, "system.process.name": "vserver", "system.process.started.time": " 141 days 22 hours 45 minutes 30 seconds", "system.process.started.time.seconds": 12264330, "system.process.threads": 1, "system.process.user": "sdb", "system.process.virtual.memory.bytes": 32313344}, {"status": "Up", "system.process": "vserver|sdbgloballistener start -m", "system.process.command": "sdbgloballistener start -m", "system.process.cpu.percent": 0, "system.process.handles": 0, "system.process.id": 1701, "system.process.memory.used.bytes": 3387392, "system.process.memory.used.percent": 0.1, "system.process.name": "vserver", "system.process.started.time": " 141 days 22 hours 45 minutes 30 seconds", "system.process.started.time.seconds": 12264330, "system.process.threads": 1, "system.process.user": "sdb", "system.process.virtual.memory.bytes": 36646912}, {"status": "Up", "system.process": "vserver|sdbgloballistener start -m", "system.process.command": "sdbgloballistener start -m", "system.process.cpu.percent": 0, "system.process.handles": 0, "system.process.id": 1702, "system.process.memory.used.bytes": 167936, "system.process.memory.used.percent": 0, "system.process.name": "vserver", "system.process.started.time": " 141 days 22 hours 45 minutes 30 seconds", "system.process.started.time.seconds": 12264330, "system.process.threads": 1, "system.process.user": "sdb", "system.process.virtual.memory.bytes": 32313344}, {"status": "Up", "system.process": "vserver|sdbgloballistener start -m", "system.process.command": "sdbgloballistener start -m", "system.process.cpu.percent": 0, "system.process.handles": 0, "system.process.id": 8213, "system.process.memory.used.bytes": 2658304, "system.process.memory.used.percent": 0.1, "system.process.name": "vserver", "system.process.started.time": " 56 days 16 hours 27 minutes 53 seconds", "system.process.started.time.seconds": 4897673, "system.process.threads": 1, "system.process.user": "sdb", "system.process.virtual.memory.bytes": 36802560}, {"status": "Up", "system.process": "vserver|/opt/sdb/MaxDB/pgm/vserver -sdbstarter 1001 1001 A -S 7200 -Y", "system.process.command": "/opt/sdb/MaxDB/pgm/vserver -sdbstarter 1001 1001 A -S 7200 -Y", "system.process.cpu.percent": 0, "system.process.handles": 0, "system.process.id": 8688, "system.process.memory.used.bytes": 2682880, "system.process.memory.used.percent": 0.1, "system.process.name": "vserver", "system.process.started.time": " 56 days 16 hours 24 minutes 20 seconds", "system.process.started.time.seconds": 4897460, "system.process.threads": 1, "system.process.user": "sdb", "system.process.virtual.memory.bytes": 37703680}, {"status": "Up", "system.process": "vserver|/opt/sdb/MaxDB/pgm/vserver -sdbstarter 1001 1001 A -S 7200 -Y", "system.process.command": "/opt/sdb/MaxDB/pgm/vserver -sdbstarter 1001 1001 A -S 7200 -Y", "system.process.cpu.percent": 0, "system.process.handles": 0, "system.process.id": 8796, "system.process.memory.used.bytes": 2682880, "system.process.memory.used.percent": 0.1, "system.process.name": "vserver", "system.process.started.time": " 56 days 16 hours 24 minutes 6 seconds", "system.process.started.time.seconds": 4897446, "system.process.threads": 1, "system.process.user": "sdb", "system.process.virtual.memory.bytes": 37703680}, {"status": "Up", "system.process": "vserver|/opt/sdb/MaxDB/pgm/vserver -sdbstarter 1001 1001 A -S 7200 -Y", "system.process.command": "/opt/sdb/MaxDB/pgm/vserver -sdbstarter 1001 1001 A -S 7200 -Y", "system.process.cpu.percent": 0, "system.process.handles": 0, "system.process.id": 1694, "system.process.memory.used.bytes": 3403776, "system.process.memory.used.percent": 0.1, "system.process.name": "vserver", "system.process.started.time": " 141 days 22 hours 45 minutes 30 seconds", "system.process.started.time.seconds": 12264330, "system.process.threads": 1, "system.process.user": "sdb", "system.process.virtual.memory.bytes": 36646912}]}, {"correlation.metrics": ["system.process.network.connection"], "system.process": [{"status": "Up", "system.process": "vserver|sdbgloballistener start -m", "system.process.command": "sdbgloballistener start -m", "system.process.cpu.percent": 0, "system.process.handles": 0, "system.process.id": 1701, "system.process.memory.used.bytes": 3387392, "system.process.memory.used.percent": 0.1, "system.process.name": "vserver", "system.process.started.time": " 141 days 22 hours 50 minutes 30 seconds", "system.process.started.time.seconds": 12264630, "system.process.threads": 1, "system.process.user": "sdb", "system.process.virtual.memory.bytes": 36646912}, {"status": "Up", "system.process": "vserver|sdbgloballistener start -m", "system.process.command": "sdbgloballistener start -m", "system.process.cpu.percent": 0, "system.process.handles": 0, "system.process.id": 1702, "system.process.memory.used.bytes": 167936, "system.process.memory.used.percent": 0, "system.process.name": "vserver", "system.process.started.time": " 141 days 22 hours 50 minutes 30 seconds", "system.process.started.time.seconds": 12264630, "system.process.threads": 1, "system.process.user": "sdb", "system.process.virtual.memory.bytes": 32313344}, {"status": "Up", "system.process": "vserver|sdbgloballistener start -m", "system.process.command": "sdbgloballistener start -m", "system.process.cpu.percent": 0, "system.process.handles": 0, "system.process.id": 8213, "system.process.memory.used.bytes": 2658304, "system.process.memory.used.percent": 0.1, "system.process.name": "vserver", "system.process.started.time": " 56 days 16 hours 32 minutes 53 seconds", "system.process.started.time.seconds": 4897973, "system.process.threads": 1, "system.process.user": "sdb", "system.process.virtual.memory.bytes": 36802560}, {"status": "Up", "system.process": "vserver|/opt/sdb/MaxDB/pgm/vserver -sdbstarter 1001 1001 A -S 7200 -Y", "system.process.command": "/opt/sdb/MaxDB/pgm/vserver -sdbstarter 1001 1001 A -S 7200 -Y", "system.process.cpu.percent": 0, "system.process.handles": 0, "system.process.id": 8688, "system.process.memory.used.bytes": 2682880, "system.process.memory.used.percent": 0.1, "system.process.name": "vserver", "system.process.started.time": " 56 days 16 hours 29 minutes 20 seconds", "system.process.started.time.seconds": 4897760, "system.process.threads": 1, "system.process.user": "sdb", "system.process.virtual.memory.bytes": 37703680}, {"status": "Up", "system.process": "vserver|/opt/sdb/MaxDB/pgm/vserver -sdbstarter 1001 1001 A -S 7200 -Y", "system.process.command": "/opt/sdb/MaxDB/pgm/vserver -sdbstarter 1001 1001 A -S 7200 -Y", "system.process.cpu.percent": 0, "system.process.handles": 0, "system.process.id": 8796, "system.process.memory.used.bytes": 2682880, "system.process.memory.used.percent": 0.1, "system.process.name": "vserver", "system.process.started.time": " 56 days 16 hours 29 minutes 6 seconds", "system.process.started.time.seconds": 4897746, "system.process.threads": 1, "system.process.user": "sdb", "system.process.virtual.memory.bytes": 37703680}, {"status": "Up", "system.process": "vserver|/opt/sdb/MaxDB/pgm/vserver -sdbstarter 1001 1001 A -S 7200 -Y", "system.process.command": "/opt/sdb/MaxDB/pgm/vserver -sdbstarter 1001 1001 A -S 7200 -Y", "system.process.cpu.percent": 0, "system.process.id": 1694, "system.process.memory.used.bytes": 3403776, "system.process.memory.used.percent": 0.1, "system.process.name": "vserver", "system.process.started.time": " 141 days 22 hours 50 minutes 30 seconds", "system.process.started.time.seconds": 12264630, "system.process.threads": 1, "system.process.user": "sdb", "system.process.virtual.memory.bytes": 36646912}, {"status": "Up", "system.process": "vserver|/opt/sdb/MaxDB/pgm/vserver -sdbstarter 1001 1001 A -S 7200 -Y", "system.process.command": "/opt/sdb/MaxDB/pgm/vserver -sdbstarter 1001 1001 A -S 7200 -Y", "system.process.cpu.percent": 0, "system.process.handles": 0, "system.process.id": 1695, "system.process.memory.used.bytes": 167936, "system.process.memory.used.percent": 0, "system.process.name": "vserver", "system.process.started.time": " 141 days 22 hours 50 minutes 30 seconds", "system.process.started.time.seconds": 12264630, "system.process.threads": 1, "system.process.user": "sdb", "system.process.virtual.memory.bytes": 32313344}]}, {"correlation.metrics": ["system.process.network.connection"], "system.process": [{"status": "Up", "system.process": "vserver|/opt/sdb/MaxDB/pgm/vserver -sdbstarter 1001 1001 A -S 7200 -Y", "system.process.command": "/opt/sdb/MaxDB/pgm/vserver -sdbstarter 1001 1001 A -S 7200 -Y", "system.process.cpu.percent": 0, "system.process.handles": 0, "system.process.id": 8688, "system.process.memory.used.bytes": 2682880, "system.process.memory.used.percent": 0.1, "system.process.name": "vserver", "system.process.started.time": " 56 days 16 hours 34 minutes 21 seconds", "system.process.started.time.seconds": 4898061, "system.process.threads": 1, "system.process.user": "sdb", "system.process.virtual.memory.bytes": 37703680}, {"status": "Up", "system.process": "vserver|/opt/sdb/MaxDB/pgm/vserver -sdbstarter 1001 1001 A -S 7200 -Y", "system.process.command": "/opt/sdb/MaxDB/pgm/vserver -sdbstarter 1001 1001 A -S 7200 -Y", "system.process.cpu.percent": 0, "system.process.handles": 0, "system.process.id": 8796, "system.process.memory.used.bytes": 2682880, "system.process.memory.used.percent": 0.1, "system.process.name": "vserver", "system.process.started.time": " 56 days 16 hours 34 minutes 7 seconds", "system.process.started.time.seconds": 4898047, "system.process.threads": 1, "system.process.user": "sdb", "system.process.virtual.memory.bytes": 37703680}, {"status": "Up", "system.process": "vserver|/opt/sdb/MaxDB/pgm/vserver -sdbstarter 1001 1001 A -S 7200 -Y", "system.process.command": "/opt/sdb/MaxDB/pgm/vserver -sdbstarter 1001 1001 A -S 7200 -Y", "system.process.cpu.percent": 0, "system.process.handles": 0, "system.process.id": 1694, "system.process.memory.used.bytes": 3403776, "system.process.memory.used.percent": 0.1, "system.process.name": "vserver", "system.process.started.time": " 141 days 22 hours 55 minutes 31 seconds", "system.process.started.time.seconds": 12264931, "system.process.threads": 1, "system.process.user": "sdb", "system.process.virtual.memory.bytes": 36646912}, {"status": "Up", "system.process": "vserver|/opt/sdb/MaxDB/pgm/vserver -sdbstarter 1001 1001 A -S 7200 -Y", "system.process.command": "/opt/sdb/MaxDB/pgm/vserver -sdbstarter 1001 1001 A -S 7200 -Y", "system.process.cpu.percent": 0, "system.process.handles": 0, "system.process.id": 1695, "system.process.memory.used.bytes": 167936, "system.process.memory.used.percent": 0, "system.process.name": "vserver", "system.process.started.time": " 141 days 22 hours 55 minutes 31 seconds", "system.process.started.time.seconds": 12264931, "system.process.threads": 1, "system.process.user": "sdb", "system.process.virtual.memory.bytes": 32313344}, {"status": "Up", "system.process": "vserver|sdbgloballistener start -m", "system.process.command": "sdbgloballistener start -m", "system.process.cpu.percent": 0, "system.process.handles": 0, "system.process.id": 1701, "system.process.memory.used.bytes": 3387392, "system.process.memory.used.percent": 0.1, "system.process.name": "vserver", "system.process.started.time": " 141 days 22 hours 55 minutes 31 seconds", "system.process.started.time.seconds": 12264931, "system.process.threads": 1, "system.process.user": "sdb", "system.process.virtual.memory.bytes": 36646912}, {"status": "Up", "system.process": "vserver|sdbgloballistener start -m", "system.process.command": "sdbgloballistener start -m", "system.process.cpu.percent": 0, "system.process.handles": 0, "system.process.id": 1702, "system.process.memory.used.bytes": 167936, "system.process.memory.used.percent": 0, "system.process.name": "vserver", "system.process.started.time": " 141 days 22 hours 55 minutes 31 seconds", "system.process.started.time.seconds": 12264931, "system.process.threads": 1, "system.process.user": "sdb", "system.process.virtual.memory.bytes": 32313344}, {"status": "Up", "system.process": "vserver|sdbgloballistener start -m", "system.process.command": "sdbgloballistener start -m", "system.process.cpu.percent": 0, "system.process.handles": 0, "system.process.id": 8213, "system.process.memory.used.bytes": 2658304, "system.process.memory.used.percent": 0.1, "system.process.name": "vserver", "system.process.started.time": " 56 days 16 hours 37 minutes 54 seconds", "system.process.started.time.seconds": 4898274, "system.process.threads": 1, "system.process.user": "sdb", "system.process.virtual.memory.bytes": 36802560}]}, {"correlation.metrics": ["system.process.network.connection"], "system.process": [{"status": "Up", "system.process": "vserver|sdbgloballistener start -m", "system.process.command": "sdbgloballistener start -m", "system.process.cpu.percent": 0, "system.process.handles": 0, "system.process.id": 1701, "system.process.memory.used.bytes": 3387392, "system.process.memory.used.percent": 0.1, "system.process.name": "vserver", "system.process.started.time": " 141 days 23 hours 5 minutes 31 seconds", "system.process.started.time.seconds": 12265531, "system.process.threads": 1, "system.process.user": "sdb", "system.process.virtual.memory.bytes": 36646912}, {"status": "Up", "system.process": "vserver|sdbgloballistener start -m", "system.process.command": "sdbgloballistener start -m", "system.process.cpu.percent": 0, "system.process.handles": 0, "system.process.id": 1702, "system.process.memory.used.bytes": 167936, "system.process.memory.used.percent": 0, "system.process.name": "vserver", "system.process.started.time": " 141 days 23 hours 5 minutes 31 seconds", "system.process.started.time.seconds": 12265531, "system.process.threads": 1, "system.process.user": "sdb", "system.process.virtual.memory.bytes": 32313344}, {"status": "Up", "system.process": "vserver|sdbgloballistener start -m", "system.process.command": "sdbgloballistener start -m", "system.process.cpu.percent": 0, "system.process.handles": 0, "system.process.id": 8213, "system.process.memory.used.bytes": 2658304, "system.process.memory.used.percent": 0.1, "system.process.name": "vserver", "system.process.started.time": " 56 days 16 hours 47 minutes 54 seconds", "system.process.started.time.seconds": 4898874, "system.process.threads": 1, "system.process.user": "sdb", "system.process.virtual.memory.bytes": 36802560}, {"status": "Up", "system.process": "vserver|/opt/sdb/MaxDB/pgm/vserver -sdbstarter 1001 1001 A -S 7200 -Y", "system.process.command": "/opt/sdb/MaxDB/pgm/vserver -sdbstarter 1001 1001 A -S 7200 -Y", "system.process.cpu.percent": 0, "system.process.handles": 0, "system.process.id": 8688, "system.process.memory.used.bytes": 2682880, "system.process.memory.used.percent": 0.1, "system.process.name": "vserver", "system.process.started.time": " 56 days 16 hours 44 minutes 21 seconds", "system.process.started.time.seconds": 4898661, "system.process.threads": 1, "system.process.user": "sdb", "system.process.virtual.memory.bytes": 37703680}, {"status": "Up", "system.process": "vserver|/opt/sdb/MaxDB/pgm/vserver -sdbstarter 1001 1001 A -S 7200 -Y", "system.process.command": "/opt/sdb/MaxDB/pgm/vserver -sdbstarter 1001 1001 A -S 7200 -Y", "system.process.cpu.percent": 0, "system.process.handles": 0, "system.process.id": 8796, "system.process.memory.used.bytes": 2682880, "system.process.memory.used.percent": 0.1, "system.process.name": "vserver", "system.process.started.time": " 56 days 16 hours 44 minutes 7 seconds", "system.process.started.time.seconds": 4898647, "system.process.threads": 1, "system.process.user": "sdb", "system.process.virtual.memory.bytes": 37703680}, {"status": "Up", "system.process": "vserver|/opt/sdb/MaxDB/pgm/vserver -sdbstarter 1001 1001 A -S 7200 -Y", "system.process.command": "/opt/sdb/MaxDB/pgm/vserver -sdbstarter 1001 1001 A -S 7200 -Y", "system.process.cpu.percent": 0, "system.process.handles": 0, "system.process.id": 1694, "system.process.memory.used.bytes": 3403776, "system.process.memory.used.percent": 0.1, "system.process.name": "vserver", "system.process.started.time": " 141 days 23 hours 5 minutes 31 seconds", "system.process.started.time.seconds": 12265531, "system.process.threads": 1, "system.process.user": "sdb", "system.process.virtual.memory.bytes": 36646912}, {"status": "Up", "system.process": "vserver|/opt/sdb/MaxDB/pgm/vserver -sdbstarter 1001 1001 A -S 7200 -Y", "system.process.command": "/opt/sdb/MaxDB/pgm/vserver -sdbstarter 1001 1001 A -S 7200 -Y", "system.process.cpu.percent": 0, "system.process.handles": 0, "system.process.id": 1695, "system.process.memory.used.bytes": 167936, "system.process.memory.used.percent": 0, "system.process.name": "vserver", "system.process.started.time": " 141 days 23 hours 5 minutes 31 seconds", "system.process.started.time.seconds": 12265531, "system.process.threads": 1, "system.process.user": "sdb", "system.process.virtual.memory.bytes": 32313344}]}], "rediscovery": null}, "***********": {"discovery": null, "collect": [{"correlation.metrics": ["system.process.network.connection"], "system.process": [{"status": "Up", "system.process": "nginx|nginx: worker process", "system.process.command": "nginx: worker process", "system.process.cpu.percent": 0, "system.process.handles": 31, "system.process.id": 1412599, "system.process.memory.used.bytes": 3461120, "system.process.memory.used.percent": 0, "system.process.name": "nginx", "system.process.started.time": " 20 days 4 hours 4 minutes 14 seconds", "system.process.started.time.seconds": 1742654, "system.process.threads": 1, "system.process.user": "www-data", "system.process.virtual.memory.bytes": 13189120}, {"status": "Up", "system.process": "nginx|nginx: worker process", "system.process.command": "nginx: worker process", "system.process.cpu.percent": 0, "system.process.handles": 31, "system.process.id": 1412606, "system.process.memory.used.bytes": 3723264, "system.process.memory.used.percent": 0, "system.process.name": "nginx", "system.process.started.time": " 20 days 4 hours 4 minutes 14 seconds", "system.process.started.time.seconds": 1742654, "system.process.threads": 1, "system.process.user": "www-data", "system.process.virtual.memory.bytes": 13189120}, {"status": "Up", "system.process": "standalo|/bin/sh /opt/wildfly/bin/standalone.sh -b=0.0.0.0", "system.process.command": "/bin/sh /opt/wildfly/bin/standalone.sh -b=0.0.0.0", "system.process.cpu.percent": 0, "system.process.handles": 9, "system.process.id": 1412828, "system.process.memory.used.bytes": 1835008, "system.process.memory.used.percent": 0, "system.process.name": "standalo", "system.process.started.time": " 20 days 4 hours 3 minutes 51 seconds", "system.process.started.time.seconds": 1742631, "system.process.threads": 1, "system.process.user": "j<PERSON>s", "system.process.virtual.memory.bytes": 2867200}, {"status": "Up", "system.process": "postgres|postgres: 16/main: checkpointer", "system.process.command": "postgres: 16/main: checkpointer", "system.process.cpu.percent": 0, "system.process.handles": 55, "system.process.id": 1414371, "system.process.memory.used.bytes": 8069120, "system.process.memory.used.percent": 0, "system.process.name": "postgres", "system.process.started.time": " 20 days 4 hours 1 minutes 38 seconds", "system.process.started.time.seconds": 1742498, "system.process.threads": 1, "system.process.user": "postgres", "system.process.virtual.memory.bytes": 229085184}, {"status": "Up", "system.process": "postgres|postgres: 16/main: autovacuum launcher", "system.process.command": "postgres: 16/main: autovacuum launcher", "system.process.cpu.percent": 0, "system.process.handles": 56, "system.process.id": 1414375, "system.process.memory.used.bytes": 4136960, "system.process.memory.used.percent": 0, "system.process.name": "postgres", "system.process.started.time": " 20 days 4 hours 1 minutes 38 seconds", "system.process.started.time.seconds": 1742498, "system.process.threads": 1, "system.process.user": "postgres", "system.process.virtual.memory.bytes": 229826560}, {"status": "Up", "system.process": "postgres|postgres: 16/main: logical replication launcher", "system.process.command": "postgres: 16/main: logical replication launcher", "system.process.cpu.percent": 0, "system.process.handles": 56, "system.process.id": 1414376, "system.process.memory.used.bytes": 4268032, "system.process.memory.used.percent": 0, "system.process.name": "postgres", "system.process.started.time": " 20 days 4 hours 1 minutes 38 seconds", "system.process.started.time.seconds": 1742498, "system.process.threads": 1, "system.process.user": "postgres", "system.process.virtual.memory.bytes": 229806080}, {"status": "Up", "system.process": "nginx|nginx: worker process", "system.process.command": "nginx: worker process", "system.process.cpu.percent": 0, "system.process.handles": 31, "system.process.id": 1412600, "system.process.memory.used.bytes": 3461120, "system.process.memory.used.percent": 0, "system.process.name": "nginx", "system.process.started.time": " 20 days 4 hours 4 minutes 14 seconds", "system.process.started.time.seconds": 1742654, "system.process.threads": 1, "system.process.user": "www-data", "system.process.virtual.memory.bytes": 13189120}, {"status": "Up", "system.process": "nginx|nginx: worker process", "system.process.command": "nginx: worker process", "system.process.cpu.percent": 0, "system.process.handles": 31, "system.process.id": 1412602, "system.process.memory.used.bytes": 3461120, "system.process.memory.used.percent": 0, "system.process.name": "nginx", "system.process.started.time": " 20 days 4 hours 4 minutes 14 seconds", "system.process.started.time.seconds": 1742654, "system.process.threads": 1, "system.process.user": "www-data", "system.process.virtual.memory.bytes": 13189120}, {"status": "Up", "system.process": "nginx|nginx: worker process", "system.process.command": "nginx: worker process", "system.process.cpu.percent": 0, "system.process.handles": 31, "system.process.id": 1412603, "system.process.memory.used.bytes": 2936832, "system.process.memory.used.percent": 0, "system.process.name": "nginx", "system.process.started.time": " 20 days 4 hours 4 minutes 14 seconds", "system.process.started.time.seconds": 1742654, "system.process.threads": 1, "system.process.user": "www-data", "system.process.virtual.memory.bytes": 13189120}, {"status": "Up", "system.process": "nginx|nginx: worker process", "system.process.command": "nginx: worker process", "system.process.cpu.percent": 0, "system.process.handles": 31, "system.process.id": 1412605, "system.process.memory.used.bytes": 3461120, "system.process.memory.used.percent": 0, "system.process.name": "nginx", "system.process.started.time": " 20 days 4 hours 4 minutes 14 seconds", "system.process.started.time.seconds": 1742654, "system.process.threads": 1, "system.process.user": "www-data", "system.process.virtual.memory.bytes": 13189120}, {"status": "Up", "system.process": "postgres|postgres: 16/main: walwriter", "system.process.command": "postgres: 16/main: walwriter", "system.process.cpu.percent": 0, "system.process.handles": 55, "system.process.id": 1414374, "system.process.memory.used.bytes": 3350528, "system.process.memory.used.percent": 0, "system.process.name": "postgres", "system.process.started.time": " 20 days 4 hours 1 minutes 38 seconds", "system.process.started.time.seconds": 1742498, "system.process.threads": 1, "system.process.user": "postgres", "system.process.virtual.memory.bytes": 228315136}, {"status": "Up", "system.process": "nginx|nginx: worker process", "system.process.command": "nginx: worker process", "system.process.cpu.percent": 0, "system.process.handles": 31, "system.process.id": 1412601, "system.process.memory.used.bytes": 3723264, "system.process.memory.used.percent": 0, "system.process.name": "nginx", "system.process.started.time": " 20 days 4 hours 4 minutes 14 seconds", "system.process.started.time.seconds": 1742654, "system.process.threads": 1, "system.process.user": "www-data", "system.process.virtual.memory.bytes": 13189120}, {"status": "Up", "system.process": "java|java -D[Standalone] -Xms64m -Xmx512m -XX:MetaspaceSize=96M -XX:MaxMetaspaceSize=256m -Djava.net.preferIPv4Stack=true -Djboss.modules.system.pkgs=org.jboss.byteman -Djava.awt.headless=true --add-exports=java.desktop/sun.awt=ALL-UNNAMED --add-exports=java.naming/com.sun.jndi.ldap=ALL-UNNAMED --add-exports=java.naming/com.sun.jndi.url.ldap=ALL-UNNAMED --add-exports=java.naming/com.sun.jndi.url.ldaps=ALL-UNNAMED --add-exports=jdk.naming.dns/com.sun.jndi.dns=ALL-UNNAMED --add-opens=java.base/java.lang=ALL-UNNAMED --add-opens=java.base/java.lang.invoke=ALL-UNNAMED --add-opens=java.base/java.lang.reflect=ALL-UNNAMED --add-opens=java.base/java.io=ALL-UNNAMED --add-opens=java.base/java.net=ALL-UNNAMED --add-opens=java.base/java.security=ALL-UNNAMED --add-opens=java.base/java.util=ALL-UNNAMED --add-opens=java.base/java.util.concurrent=ALL-UNNAMED --add-opens=java.management/javax.management=ALL-UNNAMED --add-opens=java.naming/javax.naming=ALL-UNNAMED -Djava.security.manager=allow -Dorg.jboss.boot.log.file=/opt/wildfly/standalone/log/server.log -Dlogging.configuration=file:/opt/wildfly/standalone/configuration/logging.properties -jar /opt/wildfly/jboss-modules.jar -mp /opt/wildfly/modules org.jboss.as.standalone -Djboss.home.dir=/opt/wildfly -Djboss.server.base.dir=/opt/wildfly/standalone -b=0.0.0.0", "system.process.command": "java -D[Standalone] -Xms64m -Xmx512m -XX:MetaspaceSize=96M -XX:MaxMetaspaceSize=256m -Djava.net.preferIPv4Stack=true -Djboss.modules.system.pkgs=org.jboss.byteman -Djava.awt.headless=true --add-exports=java.desktop/sun.awt=ALL-UNNAMED --add-exports=java.naming/com.sun.jndi.ldap=ALL-UNNAMED --add-exports=java.naming/com.sun.jndi.url.ldap=ALL-UNNAMED --add-exports=java.naming/com.sun.jndi.url.ldaps=ALL-UNNAMED --add-exports=jdk.naming.dns/com.sun.jndi.dns=ALL-UNNAMED --add-opens=java.base/java.lang=ALL-UNNAMED --add-opens=java.base/java.lang.invoke=ALL-UNNAMED --add-opens=java.base/java.lang.reflect=ALL-UNNAMED --add-opens=java.base/java.io=ALL-UNNAMED --add-opens=java.base/java.net=ALL-UNNAMED --add-opens=java.base/java.security=ALL-UNNAMED --add-opens=java.base/java.util=ALL-UNNAMED --add-opens=java.base/java.util.concurrent=ALL-UNNAMED --add-opens=java.management/javax.management=ALL-UNNAMED --add-opens=java.naming/javax.naming=ALL-UNNAMED -Djava.security.manager=allow -Dorg.jboss.boot.log.file=/opt/wildfly/standalone/log/server.log -Dlogging.configuration=file:/opt/wildfly/standalone/configuration/logging.properties -jar /opt/wildfly/jboss-modules.jar -mp /opt/wildfly/modules org.jboss.as.standalone -Djboss.home.dir=/opt/wildfly -Djboss.server.base.dir=/opt/wildfly/standalone -b=0.0.0.0", "system.process.cpu.percent": 0.0125, "system.process.handles": 569, "system.process.id": 1412943, "system.process.memory.used.bytes": 121901056, "system.process.memory.used.percent": 1.4, "system.process.name": "java", "system.process.started.time": " 20 days 4 hours 3 minutes 51 seconds", "system.process.started.time.seconds": 1742631, "system.process.threads": 73, "system.process.user": "j<PERSON>s", "system.process.virtual.memory.bytes": 1544192000}, {"status": "Up", "system.process": "nginx|nginx: worker process", "system.process.command": "nginx: worker process", "system.process.cpu.percent": 0, "system.process.handles": 31, "system.process.id": 1412604, "system.process.memory.used.bytes": 3461120, "system.process.memory.used.percent": 0, "system.process.name": "nginx", "system.process.started.time": " 20 days 4 hours 4 minutes 14 seconds", "system.process.started.time.seconds": 1742654, "system.process.threads": 1, "system.process.user": "www-data", "system.process.virtual.memory.bytes": 13189120}, {"status": "Up", "system.process": "postgres|/usr/lib/postgresql/16/bin/postgres -D /var/lib/postgresql/16/main -c config_file=/etc/postgresql/16/main/postgresql.conf", "system.process.command": "/usr/lib/postgresql/16/bin/postgres -D /var/lib/postgresql/16/main -c config_file=/etc/postgresql/16/main/postgresql.conf", "system.process.cpu.percent": 0, "system.process.handles": 58, "system.process.id": 1414370, "system.process.memory.used.bytes": 8781824, "system.process.memory.used.percent": 0.1, "system.process.name": "postgres", "system.process.started.time": " 20 days 4 hours 1 minutes 38 seconds", "system.process.started.time.seconds": 1742498, "system.process.threads": 1, "system.process.user": "postgres", "system.process.virtual.memory.bytes": 228179968}, {"status": "Up", "system.process": "postgres|postgres: 16/main: background writer", "system.process.command": "postgres: 16/main: background writer", "system.process.cpu.percent": 0, "system.process.handles": 54, "system.process.id": 1414372, "system.process.memory.used.bytes": 3612672, "system.process.memory.used.percent": 0, "system.process.name": "postgres", "system.process.started.time": " 20 days 4 hours 1 minutes 38 seconds", "system.process.started.time.seconds": 1742498, "system.process.threads": 1, "system.process.user": "postgres", "system.process.virtual.memory.bytes": 228315136}, {"status": "Up", "system.process": "nginx|nginx: master process /usr/sbin/nginx -g daemon on; master_process on;", "system.process.command": "nginx: master process /usr/sbin/nginx -g daemon on; master_process on;", "system.process.cpu.percent": 0, "system.process.handles": 34, "system.process.id": 1412598, "system.process.memory.used.bytes": 1757184, "system.process.memory.used.percent": 0, "system.process.name": "nginx", "system.process.started.time": " 20 days 4 hours 4 minutes 14 seconds", "system.process.started.time.seconds": 1742654, "system.process.threads": 1, "system.process.user": "root", "system.process.virtual.memory.bytes": 11423744}, {"status": "Down", "system.process": "postgres|postgres: 16/main: motadata motadata 127.0.0.1(51172) idle", "system.process.command": "postgres: 16/main: motadata motadata 127.0.0.1(51172) idle", "system.process.name": "postgres"}, {"status": "Down", "system.process": "postgres|postgres: 16/main: motadata motadata 127.0.0.1(51180) idle", "system.process.command": "postgres: 16/main: motadata motadata 127.0.0.1(51180) idle", "system.process.name": "postgres"}, {"status": "Down", "system.process": "postgres|postgres: 16/main: motadata motadata 127.0.0.1(51196) idle", "system.process.command": "postgres: 16/main: motadata motadata 127.0.0.1(51196) idle", "system.process.name": "postgres"}, {"status": "Down", "system.process": "postgres|postgres: 16/main: motadata motadata 127.0.0.1(51198) idle", "system.process.command": "postgres: 16/main: motadata motadata 127.0.0.1(51198) idle", "system.process.name": "postgres"}, {"status": "Down", "system.process": "postgres|postgres: 16/main: motadata motadata 127.0.0.1(51200) idle", "system.process.command": "postgres: 16/main: motadata motadata 127.0.0.1(51200) idle", "system.process.name": "postgres"}], "system.process.network.connection": [{"system.process": "postgres|/usr/lib/postgresql/16/bin/postgres -D /var/lib/postgresql/16/main -c config_file=/etc/postgresql/16/main/postgresql.conf", "system.process.destination.ip": "***********", "system.process.destination.port": "*", "system.process.source.ip": "***********", "system.process.source.port": "5432"}]}, {"correlation.metrics": ["system.process.network.connection"], "system.process": [{"status": "Up", "system.process": "nginx|nginx: worker process", "system.process.command": "nginx: worker process", "system.process.cpu.percent": 0, "system.process.handles": 31, "system.process.id": 1412602, "system.process.memory.used.bytes": 3461120, "system.process.memory.used.percent": 0, "system.process.name": "nginx", "system.process.started.time": " 20 days 4 hours 6 minutes 17 seconds", "system.process.started.time.seconds": 1742777, "system.process.threads": 1, "system.process.user": "www-data", "system.process.virtual.memory.bytes": 13189120}, {"status": "Up", "system.process": "nginx|nginx: worker process", "system.process.command": "nginx: worker process", "system.process.cpu.percent": 0, "system.process.handles": 31, "system.process.id": 1412603, "system.process.memory.used.bytes": 2936832, "system.process.memory.used.percent": 0, "system.process.name": "nginx", "system.process.started.time": " 20 days 4 hours 6 minutes 17 seconds", "system.process.started.time.seconds": 1742777, "system.process.threads": 1, "system.process.user": "www-data", "system.process.virtual.memory.bytes": 13189120}, {"status": "Up", "system.process": "nginx|nginx: worker process", "system.process.command": "nginx: worker process", "system.process.cpu.percent": 0, "system.process.handles": 31, "system.process.id": 1412606, "system.process.memory.used.bytes": 3723264, "system.process.memory.used.percent": 0, "system.process.name": "nginx", "system.process.started.time": " 20 days 4 hours 6 minutes 17 seconds", "system.process.started.time.seconds": 1742777, "system.process.threads": 1, "system.process.user": "www-data", "system.process.virtual.memory.bytes": 13189120}, {"status": "Up", "system.process": "postgres|postgres: 16/main: checkpointer", "system.process.command": "postgres: 16/main: checkpointer", "system.process.cpu.percent": 0, "system.process.handles": 55, "system.process.id": 1414371, "system.process.memory.used.bytes": 8069120, "system.process.memory.used.percent": 0, "system.process.name": "postgres", "system.process.started.time": " 20 days 4 hours 3 minutes 41 seconds", "system.process.started.time.seconds": 1742621, "system.process.threads": 1, "system.process.user": "postgres", "system.process.virtual.memory.bytes": 229085184}, {"status": "Up", "system.process": "nginx|nginx: worker process", "system.process.command": "nginx: worker process", "system.process.cpu.percent": 0, "system.process.handles": 31, "system.process.id": 1412599, "system.process.memory.used.bytes": 3461120, "system.process.memory.used.percent": 0, "system.process.name": "nginx", "system.process.started.time": " 20 days 4 hours 6 minutes 17 seconds", "system.process.started.time.seconds": 1742777, "system.process.threads": 1, "system.process.user": "www-data", "system.process.virtual.memory.bytes": 13189120}, {"status": "Up", "system.process": "java|java -D[Standalone] -Xms64m -Xmx512m -XX:MetaspaceSize=96M -XX:MaxMetaspaceSize=256m -Djava.net.preferIPv4Stack=true -Djboss.modules.system.pkgs=org.jboss.byteman -Djava.awt.headless=true --add-exports=java.desktop/sun.awt=ALL-UNNAMED --add-exports=java.naming/com.sun.jndi.ldap=ALL-UNNAMED --add-exports=java.naming/com.sun.jndi.url.ldap=ALL-UNNAMED --add-exports=java.naming/com.sun.jndi.url.ldaps=ALL-UNNAMED --add-exports=jdk.naming.dns/com.sun.jndi.dns=ALL-UNNAMED --add-opens=java.base/java.lang=ALL-UNNAMED --add-opens=java.base/java.lang.invoke=ALL-UNNAMED --add-opens=java.base/java.lang.reflect=ALL-UNNAMED --add-opens=java.base/java.io=ALL-UNNAMED --add-opens=java.base/java.net=ALL-UNNAMED --add-opens=java.base/java.security=ALL-UNNAMED --add-opens=java.base/java.util=ALL-UNNAMED --add-opens=java.base/java.util.concurrent=ALL-UNNAMED --add-opens=java.management/javax.management=ALL-UNNAMED --add-opens=java.naming/javax.naming=ALL-UNNAMED -Djava.security.manager=allow -Dorg.jboss.boot.log.file=/opt/wildfly/standalone/log/server.log -Dlogging.configuration=file:/opt/wildfly/standalone/configuration/logging.properties -jar /opt/wildfly/jboss-modules.jar -mp /opt/wildfly/modules org.jboss.as.standalone -Djboss.home.dir=/opt/wildfly -Djboss.server.base.dir=/opt/wildfly/standalone -b=0.0.0.0", "system.process.command": "java -D[Standalone] -Xms64m -Xmx512m -XX:MetaspaceSize=96M -XX:MaxMetaspaceSize=256m -Djava.net.preferIPv4Stack=true -Djboss.modules.system.pkgs=org.jboss.byteman -Djava.awt.headless=true --add-exports=java.desktop/sun.awt=ALL-UNNAMED --add-exports=java.naming/com.sun.jndi.ldap=ALL-UNNAMED --add-exports=java.naming/com.sun.jndi.url.ldap=ALL-UNNAMED --add-exports=java.naming/com.sun.jndi.url.ldaps=ALL-UNNAMED --add-exports=jdk.naming.dns/com.sun.jndi.dns=ALL-UNNAMED --add-opens=java.base/java.lang=ALL-UNNAMED --add-opens=java.base/java.lang.invoke=ALL-UNNAMED --add-opens=java.base/java.lang.reflect=ALL-UNNAMED --add-opens=java.base/java.io=ALL-UNNAMED --add-opens=java.base/java.net=ALL-UNNAMED --add-opens=java.base/java.security=ALL-UNNAMED --add-opens=java.base/java.util=ALL-UNNAMED --add-opens=java.base/java.util.concurrent=ALL-UNNAMED --add-opens=java.management/javax.management=ALL-UNNAMED --add-opens=java.naming/javax.naming=ALL-UNNAMED -Djava.security.manager=allow -Dorg.jboss.boot.log.file=/opt/wildfly/standalone/log/server.log -Dlogging.configuration=file:/opt/wildfly/standalone/configuration/logging.properties -jar /opt/wildfly/jboss-modules.jar -mp /opt/wildfly/modules org.jboss.as.standalone -Djboss.home.dir=/opt/wildfly -Djboss.server.base.dir=/opt/wildfly/standalone -b=0.0.0.0", "system.process.cpu.percent": 0.0125, "system.process.handles": 569, "system.process.id": 1412943, "system.process.memory.used.bytes": 122032128, "system.process.memory.used.percent": 1.4, "system.process.name": "java", "system.process.started.time": " 20 days 4 hours 5 minutes 54 seconds", "system.process.started.time.seconds": 1742754, "system.process.threads": 73, "system.process.user": "j<PERSON>s", "system.process.virtual.memory.bytes": 1544192000}, {"status": "Up", "system.process": "postgres|/usr/lib/postgresql/16/bin/postgres -D /var/lib/postgresql/16/main -c config_file=/etc/postgresql/16/main/postgresql.conf", "system.process.command": "/usr/lib/postgresql/16/bin/postgres -D /var/lib/postgresql/16/main -c config_file=/etc/postgresql/16/main/postgresql.conf", "system.process.cpu.percent": 0, "system.process.handles": 58, "system.process.id": 1414370, "system.process.memory.used.bytes": 8781824, "system.process.memory.used.percent": 0.1, "system.process.name": "postgres", "system.process.started.time": " 20 days 4 hours 3 minutes 41 seconds", "system.process.started.time.seconds": 1742621, "system.process.threads": 1, "system.process.user": "postgres", "system.process.virtual.memory.bytes": 228179968}, {"status": "Up", "system.process": "postgres|postgres: 16/main: logical replication launcher", "system.process.command": "postgres: 16/main: logical replication launcher", "system.process.cpu.percent": 0, "system.process.handles": 56, "system.process.id": 1414376, "system.process.memory.used.bytes": 4268032, "system.process.memory.used.percent": 0, "system.process.name": "postgres", "system.process.started.time": " 20 days 4 hours 3 minutes 41 seconds", "system.process.started.time.seconds": 1742621, "system.process.threads": 1, "system.process.user": "postgres", "system.process.virtual.memory.bytes": 229806080}, {"status": "Up", "system.process": "nginx|nginx: worker process", "system.process.command": "nginx: worker process", "system.process.cpu.percent": 0, "system.process.handles": 31, "system.process.id": 1412600, "system.process.memory.used.bytes": 3461120, "system.process.memory.used.percent": 0, "system.process.name": "nginx", "system.process.started.time": " 20 days 4 hours 6 minutes 17 seconds", "system.process.started.time.seconds": 1742777, "system.process.threads": 1, "system.process.user": "www-data", "system.process.virtual.memory.bytes": 13189120}, {"status": "Up", "system.process": "nginx|nginx: worker process", "system.process.command": "nginx: worker process", "system.process.cpu.percent": 0, "system.process.handles": 31, "system.process.id": 1412605, "system.process.memory.used.bytes": 3461120, "system.process.memory.used.percent": 0, "system.process.name": "nginx", "system.process.started.time": " 20 days 4 hours 6 minutes 17 seconds", "system.process.started.time.seconds": 1742777, "system.process.threads": 1, "system.process.user": "www-data", "system.process.virtual.memory.bytes": 13189120}, {"status": "Up", "system.process": "postgres|postgres: 16/main: autovacuum launcher", "system.process.command": "postgres: 16/main: autovacuum launcher", "system.process.cpu.percent": 0, "system.process.handles": 56, "system.process.id": 1414375, "system.process.memory.used.bytes": 4136960, "system.process.memory.used.percent": 0, "system.process.name": "postgres", "system.process.started.time": " 20 days 4 hours 3 minutes 41 seconds", "system.process.started.time.seconds": 1742621, "system.process.threads": 1, "system.process.user": "postgres", "system.process.virtual.memory.bytes": 229826560}, {"status": "Up", "system.process": "nginx|nginx: master process /usr/sbin/nginx -g daemon on; master_process on;", "system.process.command": "nginx: master process /usr/sbin/nginx -g daemon on; master_process on;", "system.process.cpu.percent": 0, "system.process.handles": 34, "system.process.id": 1412598, "system.process.memory.used.bytes": 1757184, "system.process.memory.used.percent": 0, "system.process.name": "nginx", "system.process.started.time": " 20 days 4 hours 6 minutes 17 seconds", "system.process.started.time.seconds": 1742777, "system.process.threads": 1, "system.process.user": "root", "system.process.virtual.memory.bytes": 11423744}, {"status": "Up", "system.process": "nginx|nginx: worker process", "system.process.command": "nginx: worker process", "system.process.cpu.percent": 0, "system.process.handles": 31, "system.process.id": 1412604, "system.process.memory.used.bytes": 3461120, "system.process.memory.used.percent": 0, "system.process.name": "nginx", "system.process.started.time": " 20 days 4 hours 6 minutes 17 seconds", "system.process.started.time.seconds": 1742777, "system.process.threads": 1, "system.process.user": "www-data", "system.process.virtual.memory.bytes": 13189120}, {"status": "Up", "system.process": "standalo|/bin/sh /opt/wildfly/bin/standalone.sh -b=0.0.0.0", "system.process.command": "/bin/sh /opt/wildfly/bin/standalone.sh -b=0.0.0.0", "system.process.cpu.percent": 0, "system.process.handles": 9, "system.process.id": 1412828, "system.process.memory.used.bytes": 1835008, "system.process.memory.used.percent": 0, "system.process.name": "standalo", "system.process.started.time": " 20 days 4 hours 5 minutes 54 seconds", "system.process.started.time.seconds": 1742754, "system.process.threads": 1, "system.process.user": "j<PERSON>s", "system.process.virtual.memory.bytes": 2867200}, {"status": "Up", "system.process": "postgres|postgres: 16/main: background writer", "system.process.command": "postgres: 16/main: background writer", "system.process.cpu.percent": 0, "system.process.handles": 54, "system.process.id": 1414372, "system.process.memory.used.bytes": 3612672, "system.process.memory.used.percent": 0, "system.process.name": "postgres", "system.process.started.time": " 20 days 4 hours 3 minutes 41 seconds", "system.process.started.time.seconds": 1742621, "system.process.threads": 1, "system.process.user": "postgres", "system.process.virtual.memory.bytes": 228315136}, {"status": "Up", "system.process": "postgres|postgres: 16/main: walwriter", "system.process.command": "postgres: 16/main: walwriter", "system.process.cpu.percent": 0, "system.process.handles": 55, "system.process.id": 1414374, "system.process.memory.used.bytes": 3350528, "system.process.memory.used.percent": 0, "system.process.name": "postgres", "system.process.started.time": " 20 days 4 hours 3 minutes 41 seconds", "system.process.started.time.seconds": 1742621, "system.process.threads": 1, "system.process.user": "postgres", "system.process.virtual.memory.bytes": 228315136}, {"status": "Up", "system.process": "nginx|nginx: worker process", "system.process.command": "nginx: worker process", "system.process.cpu.percent": 0, "system.process.handles": 31, "system.process.id": 1412601, "system.process.memory.used.bytes": 3723264, "system.process.memory.used.percent": 0, "system.process.name": "nginx", "system.process.started.time": " 20 days 4 hours 6 minutes 17 seconds", "system.process.started.time.seconds": 1742777, "system.process.threads": 1, "system.process.user": "www-data", "system.process.virtual.memory.bytes": 13189120}, {"status": "Down", "system.process": "postgres|postgres: 16/main: motadata motadata 127.0.0.1(51172) idle", "system.process.command": "postgres: 16/main: motadata motadata 127.0.0.1(51172) idle", "system.process.name": "postgres"}, {"status": "Down", "system.process": "postgres|postgres: 16/main: motadata motadata 127.0.0.1(51180) idle", "system.process.command": "postgres: 16/main: motadata motadata 127.0.0.1(51180) idle", "system.process.name": "postgres"}, {"status": "Down", "system.process": "postgres|postgres: 16/main: motadata motadata 127.0.0.1(51196) idle", "system.process.command": "postgres: 16/main: motadata motadata 127.0.0.1(51196) idle", "system.process.name": "postgres"}, {"status": "Down", "system.process": "postgres|postgres: 16/main: motadata motadata 127.0.0.1(51198) idle", "system.process.command": "postgres: 16/main: motadata motadata 127.0.0.1(51198) idle", "system.process.name": "postgres"}, {"status": "Down", "system.process": "postgres|postgres: 16/main: motadata motadata 127.0.0.1(51200) idle", "system.process.command": "postgres: 16/main: motadata motadata 127.0.0.1(51200) idle", "system.process.name": "postgres"}], "system.process.network.connection": [{"system.process": "postgres|/usr/lib/postgresql/16/bin/postgres -D /var/lib/postgresql/16/main -c config_file=/etc/postgresql/16/main/postgresql.conf", "system.process.destination.ip": "***********", "system.process.destination.port": "*", "system.process.source.ip": "***********", "system.process.source.port": "5432"}]}, {"correlation.metrics": ["system.process.network.connection"], "system.process": [{"status": "Up", "system.process": "nginx|nginx: worker process", "system.process.command": "nginx: worker process", "system.process.cpu.percent": 0, "system.process.handles": 31, "system.process.id": 1412599, "system.process.memory.used.bytes": 3461120, "system.process.memory.used.percent": 0, "system.process.name": "nginx", "system.process.started.time": " 20 days 4 hours 11 minutes 17 seconds", "system.process.started.time.seconds": 1743077, "system.process.threads": 1, "system.process.user": "www-data", "system.process.virtual.memory.bytes": 13189120}, {"status": "Up", "system.process": "nginx|nginx: worker process", "system.process.command": "nginx: worker process", "system.process.cpu.percent": 0, "system.process.handles": 31, "system.process.id": 1412603, "system.process.memory.used.bytes": 2936832, "system.process.memory.used.percent": 0, "system.process.name": "nginx", "system.process.started.time": " 20 days 4 hours 11 minutes 17 seconds", "system.process.started.time.seconds": 1743077, "system.process.threads": 1, "system.process.user": "www-data", "system.process.virtual.memory.bytes": 13189120}, {"status": "Up", "system.process": "nginx|nginx: worker process", "system.process.command": "nginx: worker process", "system.process.cpu.percent": 0, "system.process.handles": 31, "system.process.id": 1412604, "system.process.memory.used.bytes": 3461120, "system.process.memory.used.percent": 0, "system.process.name": "nginx", "system.process.started.time": " 20 days 4 hours 11 minutes 17 seconds", "system.process.started.time.seconds": 1743077, "system.process.threads": 1, "system.process.user": "www-data", "system.process.virtual.memory.bytes": 13189120}, {"status": "Up", "system.process": "java|java -D[Standalone] -Xms64m -Xmx512m -XX:MetaspaceSize=96M -XX:MaxMetaspaceSize=256m -Djava.net.preferIPv4Stack=true -Djboss.modules.system.pkgs=org.jboss.byteman -Djava.awt.headless=true --add-exports=java.desktop/sun.awt=ALL-UNNAMED --add-exports=java.naming/com.sun.jndi.ldap=ALL-UNNAMED --add-exports=java.naming/com.sun.jndi.url.ldap=ALL-UNNAMED --add-exports=java.naming/com.sun.jndi.url.ldaps=ALL-UNNAMED --add-exports=jdk.naming.dns/com.sun.jndi.dns=ALL-UNNAMED --add-opens=java.base/java.lang=ALL-UNNAMED --add-opens=java.base/java.lang.invoke=ALL-UNNAMED --add-opens=java.base/java.lang.reflect=ALL-UNNAMED --add-opens=java.base/java.io=ALL-UNNAMED --add-opens=java.base/java.net=ALL-UNNAMED --add-opens=java.base/java.security=ALL-UNNAMED --add-opens=java.base/java.util=ALL-UNNAMED --add-opens=java.base/java.util.concurrent=ALL-UNNAMED --add-opens=java.management/javax.management=ALL-UNNAMED --add-opens=java.naming/javax.naming=ALL-UNNAMED -Djava.security.manager=allow -Dorg.jboss.boot.log.file=/opt/wildfly/standalone/log/server.log -Dlogging.configuration=file:/opt/wildfly/standalone/configuration/logging.properties -jar /opt/wildfly/jboss-modules.jar -mp /opt/wildfly/modules org.jboss.as.standalone -Djboss.home.dir=/opt/wildfly -Djboss.server.base.dir=/opt/wildfly/standalone -b=0.0.0.0", "system.process.command": "java -D[Standalone] -Xms64m -Xmx512m -XX:MetaspaceSize=96M -XX:MaxMetaspaceSize=256m -Djava.net.preferIPv4Stack=true -Djboss.modules.system.pkgs=org.jboss.byteman -Djava.awt.headless=true --add-exports=java.desktop/sun.awt=ALL-UNNAMED --add-exports=java.naming/com.sun.jndi.ldap=ALL-UNNAMED --add-exports=java.naming/com.sun.jndi.url.ldap=ALL-UNNAMED --add-exports=java.naming/com.sun.jndi.url.ldaps=ALL-UNNAMED --add-exports=jdk.naming.dns/com.sun.jndi.dns=ALL-UNNAMED --add-opens=java.base/java.lang=ALL-UNNAMED --add-opens=java.base/java.lang.invoke=ALL-UNNAMED --add-opens=java.base/java.lang.reflect=ALL-UNNAMED --add-opens=java.base/java.io=ALL-UNNAMED --add-opens=java.base/java.net=ALL-UNNAMED --add-opens=java.base/java.security=ALL-UNNAMED --add-opens=java.base/java.util=ALL-UNNAMED --add-opens=java.base/java.util.concurrent=ALL-UNNAMED --add-opens=java.management/javax.management=ALL-UNNAMED --add-opens=java.naming/javax.naming=ALL-UNNAMED -Djava.security.manager=allow -Dorg.jboss.boot.log.file=/opt/wildfly/standalone/log/server.log -Dlogging.configuration=file:/opt/wildfly/standalone/configuration/logging.properties -jar /opt/wildfly/jboss-modules.jar -mp /opt/wildfly/modules org.jboss.as.standalone -Djboss.home.dir=/opt/wildfly -Djboss.server.base.dir=/opt/wildfly/standalone -b=0.0.0.0", "system.process.cpu.percent": 0.0125, "system.process.handles": 569, "system.process.id": 1412943, "system.process.memory.used.bytes": 122163200, "system.process.memory.used.percent": 1.4, "system.process.name": "java", "system.process.started.time": " 20 days 4 hours 10 minutes 54 seconds", "system.process.started.time.seconds": 1743054, "system.process.threads": 73, "system.process.user": "j<PERSON>s", "system.process.virtual.memory.bytes": 1544192000}, {"status": "Up", "system.process": "postgres|postgres: 16/main: checkpointer", "system.process.command": "postgres: 16/main: checkpointer", "system.process.cpu.percent": 0, "system.process.handles": 55, "system.process.id": 1414371, "system.process.memory.used.bytes": 8069120, "system.process.memory.used.percent": 0, "system.process.name": "postgres", "system.process.started.time": " 20 days 4 hours 8 minutes 41 seconds", "system.process.started.time.seconds": 1742921, "system.process.threads": 1, "system.process.user": "postgres", "system.process.virtual.memory.bytes": 229085184}, {"status": "Up", "system.process": "postgres|postgres: 16/main: walwriter", "system.process.command": "postgres: 16/main: walwriter", "system.process.cpu.percent": 0, "system.process.handles": 55, "system.process.id": 1414374, "system.process.memory.used.bytes": 3350528, "system.process.memory.used.percent": 0, "system.process.name": "postgres", "system.process.started.time": " 20 days 4 hours 8 minutes 41 seconds", "system.process.started.time.seconds": 1742921, "system.process.threads": 1, "system.process.user": "postgres", "system.process.virtual.memory.bytes": 228315136}, {"status": "Up", "system.process": "postgres|postgres: 16/main: autovacuum launcher", "system.process.command": "postgres: 16/main: autovacuum launcher", "system.process.cpu.percent": 0, "system.process.handles": 56, "system.process.id": 1414375, "system.process.memory.used.bytes": 4136960, "system.process.memory.used.percent": 0, "system.process.name": "postgres", "system.process.started.time": " 20 days 4 hours 8 minutes 41 seconds", "system.process.started.time.seconds": 1742921, "system.process.threads": 1, "system.process.user": "postgres", "system.process.virtual.memory.bytes": 229826560}, {"status": "Up", "system.process": "postgres|postgres: 16/main: logical replication launcher", "system.process.command": "postgres: 16/main: logical replication launcher", "system.process.cpu.percent": 0, "system.process.handles": 56, "system.process.id": 1414376, "system.process.memory.used.bytes": 4268032, "system.process.memory.used.percent": 0, "system.process.name": "postgres", "system.process.started.time": " 20 days 4 hours 8 minutes 41 seconds", "system.process.started.time.seconds": 1742921, "system.process.threads": 1, "system.process.user": "postgres", "system.process.virtual.memory.bytes": 229806080}, {"status": "Up", "system.process": "standalo|/bin/sh /opt/wildfly/bin/standalone.sh -b=0.0.0.0", "system.process.command": "/bin/sh /opt/wildfly/bin/standalone.sh -b=0.0.0.0", "system.process.cpu.percent": 0, "system.process.handles": 9, "system.process.id": 1412828, "system.process.memory.used.bytes": 1835008, "system.process.memory.used.percent": 0, "system.process.name": "standalo", "system.process.started.time": " 20 days 4 hours 10 minutes 55 seconds", "system.process.started.time.seconds": 1743055, "system.process.threads": 1, "system.process.user": "j<PERSON>s", "system.process.virtual.memory.bytes": 2867200}, {"status": "Up", "system.process": "postgres|/usr/lib/postgresql/16/bin/postgres -D /var/lib/postgresql/16/main -c config_file=/etc/postgresql/16/main/postgresql.conf", "system.process.command": "/usr/lib/postgresql/16/bin/postgres -D /var/lib/postgresql/16/main -c config_file=/etc/postgresql/16/main/postgresql.conf", "system.process.cpu.percent": 0, "system.process.handles": 58, "system.process.id": 1414370, "system.process.memory.used.bytes": 8781824, "system.process.memory.used.percent": 0.1, "system.process.name": "postgres", "system.process.started.time": " 20 days 4 hours 8 minutes 41 seconds", "system.process.started.time.seconds": 1742921, "system.process.threads": 1, "system.process.user": "postgres", "system.process.virtual.memory.bytes": 228179968}, {"status": "Up", "system.process": "postgres|postgres: 16/main: background writer", "system.process.command": "postgres: 16/main: background writer", "system.process.cpu.percent": 0, "system.process.handles": 54, "system.process.id": 1414372, "system.process.memory.used.bytes": 3612672, "system.process.memory.used.percent": 0, "system.process.name": "postgres", "system.process.started.time": " 20 days 4 hours 8 minutes 41 seconds", "system.process.started.time.seconds": 1742921, "system.process.threads": 1, "system.process.user": "postgres", "system.process.virtual.memory.bytes": 228315136}, {"status": "Up", "system.process": "nginx|nginx: master process /usr/sbin/nginx -g daemon on; master_process on;", "system.process.command": "nginx: master process /usr/sbin/nginx -g daemon on; master_process on;", "system.process.cpu.percent": 0, "system.process.handles": 34, "system.process.id": 1412598, "system.process.memory.used.bytes": 1757184, "system.process.memory.used.percent": 0, "system.process.name": "nginx", "system.process.started.time": " 20 days 4 hours 11 minutes 17 seconds", "system.process.started.time.seconds": 1743077, "system.process.threads": 1, "system.process.user": "root", "system.process.virtual.memory.bytes": 11423744}, {"status": "Up", "system.process": "nginx|nginx: worker process", "system.process.command": "nginx: worker process", "system.process.cpu.percent": 0, "system.process.handles": 31, "system.process.id": 1412600, "system.process.memory.used.bytes": 3461120, "system.process.memory.used.percent": 0, "system.process.name": "nginx", "system.process.started.time": " 20 days 4 hours 11 minutes 17 seconds", "system.process.started.time.seconds": 1743077, "system.process.threads": 1, "system.process.user": "www-data", "system.process.virtual.memory.bytes": 13189120}, {"status": "Up", "system.process": "nginx|nginx: worker process", "system.process.command": "nginx: worker process", "system.process.cpu.percent": 0, "system.process.handles": 31, "system.process.id": 1412601, "system.process.memory.used.bytes": 3723264, "system.process.memory.used.percent": 0, "system.process.name": "nginx", "system.process.started.time": " 20 days 4 hours 11 minutes 17 seconds", "system.process.started.time.seconds": 1743077, "system.process.threads": 1, "system.process.user": "www-data", "system.process.virtual.memory.bytes": 13189120}, {"status": "Up", "system.process": "nginx|nginx: worker process", "system.process.command": "nginx: worker process", "system.process.cpu.percent": 0, "system.process.handles": 31, "system.process.id": 1412602, "system.process.memory.used.bytes": 3461120, "system.process.memory.used.percent": 0, "system.process.name": "nginx", "system.process.started.time": " 20 days 4 hours 11 minutes 17 seconds", "system.process.started.time.seconds": 1743077, "system.process.threads": 1, "system.process.user": "www-data", "system.process.virtual.memory.bytes": 13189120}, {"status": "Up", "system.process": "nginx|nginx: worker process", "system.process.command": "nginx: worker process", "system.process.cpu.percent": 0, "system.process.handles": 31, "system.process.id": 1412605, "system.process.memory.used.bytes": 3461120, "system.process.memory.used.percent": 0, "system.process.name": "nginx", "system.process.started.time": " 20 days 4 hours 11 minutes 17 seconds", "system.process.started.time.seconds": 1743077, "system.process.threads": 1, "system.process.user": "www-data", "system.process.virtual.memory.bytes": 13189120}, {"status": "Up", "system.process": "nginx|nginx: worker process", "system.process.command": "nginx: worker process", "system.process.cpu.percent": 0, "system.process.handles": 31, "system.process.id": 1412606, "system.process.memory.used.bytes": 3723264, "system.process.memory.used.percent": 0, "system.process.name": "nginx", "system.process.started.time": " 20 days 4 hours 11 minutes 17 seconds", "system.process.started.time.seconds": 1743077, "system.process.threads": 1, "system.process.user": "www-data", "system.process.virtual.memory.bytes": 13189120}, {"status": "Down", "system.process": "postgres|postgres: 16/main: motadata motadata 127.0.0.1(51172) idle", "system.process.command": "postgres: 16/main: motadata motadata 127.0.0.1(51172) idle", "system.process.name": "postgres"}, {"status": "Down", "system.process": "postgres|postgres: 16/main: motadata motadata 127.0.0.1(51180) idle", "system.process.command": "postgres: 16/main: motadata motadata 127.0.0.1(51180) idle", "system.process.name": "postgres"}, {"status": "Down", "system.process": "postgres|postgres: 16/main: motadata motadata 127.0.0.1(51196) idle", "system.process.command": "postgres: 16/main: motadata motadata 127.0.0.1(51196) idle", "system.process.name": "postgres"}, {"status": "Down", "system.process": "postgres|postgres: 16/main: motadata motadata 127.0.0.1(51198) idle", "system.process.command": "postgres: 16/main: motadata motadata 127.0.0.1(51198) idle", "system.process.name": "postgres"}, {"status": "Down", "system.process": "postgres|postgres: 16/main: motadata motadata 127.0.0.1(51200) idle", "system.process.command": "postgres: 16/main: motadata motadata 127.0.0.1(51200) idle", "system.process.name": "postgres"}], "system.process.network.connection": [{"system.process": "postgres|/usr/lib/postgresql/16/bin/postgres -D /var/lib/postgresql/16/main -c config_file=/etc/postgresql/16/main/postgresql.conf", "system.process.destination.ip": "***********", "system.process.destination.port": "*", "system.process.source.ip": "***********", "system.process.source.port": "5432"}]}, {"correlation.metrics": ["system.process.network.connection"], "system.process": [{"status": "Up", "system.process": "nginx|nginx: worker process", "system.process.command": "nginx: worker process", "system.process.cpu.percent": 0, "system.process.handles": 31, "system.process.id": 1412606, "system.process.memory.used.bytes": 3723264, "system.process.memory.used.percent": 0, "system.process.name": "nginx", "system.process.started.time": " 20 days 4 hours 16 minutes 17 seconds", "system.process.started.time.seconds": 1743377, "system.process.threads": 1, "system.process.user": "www-data", "system.process.virtual.memory.bytes": 13189120}, {"status": "Up", "system.process": "postgres|/usr/lib/postgresql/16/bin/postgres -D /var/lib/postgresql/16/main -c config_file=/etc/postgresql/16/main/postgresql.conf", "system.process.command": "/usr/lib/postgresql/16/bin/postgres -D /var/lib/postgresql/16/main -c config_file=/etc/postgresql/16/main/postgresql.conf", "system.process.cpu.percent": 0, "system.process.handles": 58, "system.process.id": 1414370, "system.process.memory.used.bytes": 8781824, "system.process.memory.used.percent": 0.1, "system.process.name": "postgres", "system.process.started.time": " 20 days 4 hours 13 minutes 41 seconds", "system.process.started.time.seconds": 1743221, "system.process.threads": 1, "system.process.user": "postgres", "system.process.virtual.memory.bytes": 228179968}, {"status": "Up", "system.process": "postgres|postgres: 16/main: walwriter", "system.process.command": "postgres: 16/main: walwriter", "system.process.cpu.percent": 0, "system.process.handles": 55, "system.process.id": 1414374, "system.process.memory.used.bytes": 3350528, "system.process.memory.used.percent": 0, "system.process.name": "postgres", "system.process.started.time": " 20 days 4 hours 13 minutes 40 seconds", "system.process.started.time.seconds": 1743220, "system.process.threads": 1, "system.process.user": "postgres", "system.process.virtual.memory.bytes": 228315136}, {"status": "Up", "system.process": "postgres|postgres: 16/main: autovacuum launcher", "system.process.command": "postgres: 16/main: autovacuum launcher", "system.process.cpu.percent": 0, "system.process.handles": 56, "system.process.id": 1414375, "system.process.memory.used.bytes": 4136960, "system.process.memory.used.percent": 0, "system.process.name": "postgres", "system.process.started.time": " 20 days 4 hours 13 minutes 40 seconds", "system.process.started.time.seconds": 1743220, "system.process.threads": 1, "system.process.user": "postgres", "system.process.virtual.memory.bytes": 229826560}, {"status": "Up", "system.process": "nginx|nginx: worker process", "system.process.command": "nginx: worker process", "system.process.cpu.percent": 0, "system.process.handles": 31, "system.process.id": 1412602, "system.process.memory.used.bytes": 3461120, "system.process.memory.used.percent": 0, "system.process.name": "nginx", "system.process.started.time": " 20 days 4 hours 16 minutes 17 seconds", "system.process.started.time.seconds": 1743377, "system.process.threads": 1, "system.process.user": "www-data", "system.process.virtual.memory.bytes": 13189120}, {"status": "Up", "system.process": "standalo|/bin/sh /opt/wildfly/bin/standalone.sh -b=0.0.0.0", "system.process.command": "/bin/sh /opt/wildfly/bin/standalone.sh -b=0.0.0.0", "system.process.cpu.percent": 0, "system.process.handles": 9, "system.process.id": 1412828, "system.process.memory.used.bytes": 1835008, "system.process.memory.used.percent": 0, "system.process.name": "standalo", "system.process.started.time": " 20 days 4 hours 15 minutes 54 seconds", "system.process.started.time.seconds": 1743354, "system.process.threads": 1, "system.process.user": "j<PERSON>s", "system.process.virtual.memory.bytes": 2867200}, {"status": "Up", "system.process": "nginx|nginx: master process /usr/sbin/nginx -g daemon on; master_process on;", "system.process.command": "nginx: master process /usr/sbin/nginx -g daemon on; master_process on;", "system.process.cpu.percent": 0, "system.process.handles": 34, "system.process.id": 1412598, "system.process.memory.used.bytes": 1757184, "system.process.memory.used.percent": 0, "system.process.name": "nginx", "system.process.started.time": " 20 days 4 hours 16 minutes 17 seconds", "system.process.started.time.seconds": 1743377, "system.process.threads": 1, "system.process.user": "root", "system.process.virtual.memory.bytes": 11423744}, {"status": "Up", "system.process": "nginx|nginx: worker process", "system.process.command": "nginx: worker process", "system.process.cpu.percent": 0, "system.process.handles": 31, "system.process.id": 1412599, "system.process.memory.used.bytes": 3461120, "system.process.memory.used.percent": 0, "system.process.name": "nginx", "system.process.started.time": " 20 days 4 hours 16 minutes 17 seconds", "system.process.started.time.seconds": 1743377, "system.process.threads": 1, "system.process.user": "www-data", "system.process.virtual.memory.bytes": 13189120}, {"status": "Up", "system.process": "nginx|nginx: worker process", "system.process.command": "nginx: worker process", "system.process.cpu.percent": 0, "system.process.handles": 31, "system.process.id": 1412600, "system.process.memory.used.bytes": 3461120, "system.process.memory.used.percent": 0, "system.process.name": "nginx", "system.process.started.time": " 20 days 4 hours 16 minutes 17 seconds", "system.process.started.time.seconds": 1743377, "system.process.threads": 1, "system.process.user": "www-data", "system.process.virtual.memory.bytes": 13189120}, {"status": "Up", "system.process": "nginx|nginx: worker process", "system.process.command": "nginx: worker process", "system.process.cpu.percent": 0, "system.process.handles": 31, "system.process.id": 1412603, "system.process.memory.used.bytes": 2936832, "system.process.memory.used.percent": 0, "system.process.name": "nginx", "system.process.started.time": " 20 days 4 hours 16 minutes 17 seconds", "system.process.started.time.seconds": 1743377, "system.process.threads": 1, "system.process.user": "www-data", "system.process.virtual.memory.bytes": 13189120}, {"status": "Up", "system.process": "nginx|nginx: worker process", "system.process.command": "nginx: worker process", "system.process.cpu.percent": 0, "system.process.handles": 31, "system.process.id": 1412604, "system.process.memory.used.bytes": 3461120, "system.process.memory.used.percent": 0, "system.process.name": "nginx", "system.process.started.time": " 20 days 4 hours 16 minutes 17 seconds", "system.process.started.time.seconds": 1743377, "system.process.threads": 1, "system.process.user": "www-data", "system.process.virtual.memory.bytes": 13189120}, {"status": "Up", "system.process": "java|java -D[Standalone] -Xms64m -Xmx512m -XX:MetaspaceSize=96M -XX:MaxMetaspaceSize=256m -Djava.net.preferIPv4Stack=true -Djboss.modules.system.pkgs=org.jboss.byteman -Djava.awt.headless=true --add-exports=java.desktop/sun.awt=ALL-UNNAMED --add-exports=java.naming/com.sun.jndi.ldap=ALL-UNNAMED --add-exports=java.naming/com.sun.jndi.url.ldap=ALL-UNNAMED --add-exports=java.naming/com.sun.jndi.url.ldaps=ALL-UNNAMED --add-exports=jdk.naming.dns/com.sun.jndi.dns=ALL-UNNAMED --add-opens=java.base/java.lang=ALL-UNNAMED --add-opens=java.base/java.lang.invoke=ALL-UNNAMED --add-opens=java.base/java.lang.reflect=ALL-UNNAMED --add-opens=java.base/java.io=ALL-UNNAMED --add-opens=java.base/java.net=ALL-UNNAMED --add-opens=java.base/java.security=ALL-UNNAMED --add-opens=java.base/java.util=ALL-UNNAMED --add-opens=java.base/java.util.concurrent=ALL-UNNAMED --add-opens=java.management/javax.management=ALL-UNNAMED --add-opens=java.naming/javax.naming=ALL-UNNAMED -Djava.security.manager=allow -Dorg.jboss.boot.log.file=/opt/wildfly/standalone/log/server.log -Dlogging.configuration=file:/opt/wildfly/standalone/configuration/logging.properties -jar /opt/wildfly/jboss-modules.jar -mp /opt/wildfly/modules org.jboss.as.standalone -Djboss.home.dir=/opt/wildfly -Djboss.server.base.dir=/opt/wildfly/standalone -b=0.0.0.0", "system.process.command": "java -D[Standalone] -Xms64m -Xmx512m -XX:MetaspaceSize=96M -XX:MaxMetaspaceSize=256m -Djava.net.preferIPv4Stack=true -Djboss.modules.system.pkgs=org.jboss.byteman -Djava.awt.headless=true --add-exports=java.desktop/sun.awt=ALL-UNNAMED --add-exports=java.naming/com.sun.jndi.ldap=ALL-UNNAMED --add-exports=java.naming/com.sun.jndi.url.ldap=ALL-UNNAMED --add-exports=java.naming/com.sun.jndi.url.ldaps=ALL-UNNAMED --add-exports=jdk.naming.dns/com.sun.jndi.dns=ALL-UNNAMED --add-opens=java.base/java.lang=ALL-UNNAMED --add-opens=java.base/java.lang.invoke=ALL-UNNAMED --add-opens=java.base/java.lang.reflect=ALL-UNNAMED --add-opens=java.base/java.io=ALL-UNNAMED --add-opens=java.base/java.net=ALL-UNNAMED --add-opens=java.base/java.security=ALL-UNNAMED --add-opens=java.base/java.util=ALL-UNNAMED --add-opens=java.base/java.util.concurrent=ALL-UNNAMED --add-opens=java.management/javax.management=ALL-UNNAMED --add-opens=java.naming/javax.naming=ALL-UNNAMED -Djava.security.manager=allow -Dorg.jboss.boot.log.file=/opt/wildfly/standalone/log/server.log -Dlogging.configuration=file:/opt/wildfly/standalone/configuration/logging.properties -jar /opt/wildfly/jboss-modules.jar -mp /opt/wildfly/modules org.jboss.as.standalone -Djboss.home.dir=/opt/wildfly -Djboss.server.base.dir=/opt/wildfly/standalone -b=0.0.0.0", "system.process.cpu.percent": 0.0125, "system.process.handles": 569, "system.process.id": 1412943, "system.process.memory.used.bytes": 123604992, "system.process.memory.used.percent": 1.4, "system.process.name": "java", "system.process.started.time": " 20 days 4 hours 15 minutes 54 seconds", "system.process.started.time.seconds": 1743354, "system.process.threads": 73, "system.process.user": "j<PERSON>s", "system.process.virtual.memory.bytes": 1544192000}, {"status": "Up", "system.process": "postgres|postgres: 16/main: checkpointer", "system.process.command": "postgres: 16/main: checkpointer", "system.process.cpu.percent": 0, "system.process.handles": 55, "system.process.id": 1414371, "system.process.memory.used.bytes": 8069120, "system.process.memory.used.percent": 0, "system.process.name": "postgres", "system.process.started.time": " 20 days 4 hours 13 minutes 40 seconds", "system.process.started.time.seconds": 1743220, "system.process.threads": 1, "system.process.user": "postgres", "system.process.virtual.memory.bytes": 229085184}, {"status": "Up", "system.process": "nginx|nginx: worker process", "system.process.command": "nginx: worker process", "system.process.cpu.percent": 0, "system.process.handles": 31, "system.process.id": 1412601, "system.process.memory.used.bytes": 3723264, "system.process.memory.used.percent": 0, "system.process.name": "nginx", "system.process.started.time": " 20 days 4 hours 16 minutes 17 seconds", "system.process.started.time.seconds": 1743377, "system.process.threads": 1, "system.process.user": "www-data", "system.process.virtual.memory.bytes": 13189120}, {"status": "Up", "system.process": "nginx|nginx: worker process", "system.process.command": "nginx: worker process", "system.process.cpu.percent": 0, "system.process.handles": 31, "system.process.id": 1412605, "system.process.memory.used.bytes": 3461120, "system.process.memory.used.percent": 0, "system.process.name": "nginx", "system.process.started.time": " 20 days 4 hours 16 minutes 17 seconds", "system.process.started.time.seconds": 1743377, "system.process.threads": 1, "system.process.user": "www-data", "system.process.virtual.memory.bytes": 13189120}, {"status": "Up", "system.process": "postgres|postgres: 16/main: background writer", "system.process.command": "postgres: 16/main: background writer", "system.process.cpu.percent": 0, "system.process.handles": 54, "system.process.id": 1414372, "system.process.memory.used.bytes": 3612672, "system.process.memory.used.percent": 0, "system.process.name": "postgres", "system.process.started.time": " 20 days 4 hours 13 minutes 40 seconds", "system.process.started.time.seconds": 1743220, "system.process.threads": 1, "system.process.user": "postgres", "system.process.virtual.memory.bytes": 228315136}, {"status": "Up", "system.process": "postgres|postgres: 16/main: logical replication launcher", "system.process.command": "postgres: 16/main: logical replication launcher", "system.process.cpu.percent": 0, "system.process.handles": 56, "system.process.id": 1414376, "system.process.memory.used.bytes": 4268032, "system.process.memory.used.percent": 0, "system.process.name": "postgres", "system.process.started.time": " 20 days 4 hours 13 minutes 40 seconds", "system.process.started.time.seconds": 1743220, "system.process.threads": 1, "system.process.user": "postgres", "system.process.virtual.memory.bytes": 229806080}, {"status": "Down", "system.process": "postgres|postgres: 16/main: motadata motadata 127.0.0.1(51172) idle", "system.process.command": "postgres: 16/main: motadata motadata 127.0.0.1(51172) idle", "system.process.name": "postgres"}, {"status": "Down", "system.process": "postgres|postgres: 16/main: motadata motadata 127.0.0.1(51180) idle", "system.process.command": "postgres: 16/main: motadata motadata 127.0.0.1(51180) idle", "system.process.name": "postgres"}, {"status": "Down", "system.process": "postgres|postgres: 16/main: motadata motadata 127.0.0.1(51196) idle", "system.process.command": "postgres: 16/main: motadata motadata 127.0.0.1(51196) idle", "system.process.name": "postgres"}, {"status": "Down", "system.process": "postgres|postgres: 16/main: motadata motadata 127.0.0.1(51198) idle", "system.process.command": "postgres: 16/main: motadata motadata 127.0.0.1(51198) idle", "system.process.name": "postgres"}, {"status": "Down", "system.process": "postgres|postgres: 16/main: motadata motadata 127.0.0.1(51200) idle", "system.process.command": "postgres: 16/main: motadata motadata 127.0.0.1(51200) idle", "system.process.name": "postgres"}], "system.process.network.connection": [{"system.process": "postgres|/usr/lib/postgresql/16/bin/postgres -D /var/lib/postgresql/16/main -c config_file=/etc/postgresql/16/main/postgresql.conf", "system.process.destination.ip": "***********", "system.process.destination.port": "*", "system.process.source.ip": "***********", "system.process.source.port": "5432"}]}, {"correlation.metrics": ["system.process.network.connection"], "system.process": [{"status": "Up", "system.process": "nginx|nginx: worker process", "system.process.command": "nginx: worker process", "system.process.cpu.percent": 0, "system.process.handles": 31, "system.process.id": 1412599, "system.process.memory.used.bytes": 3461120, "system.process.memory.used.percent": 0, "system.process.name": "nginx", "system.process.started.time": " 20 days 4 hours 21 minutes 19 seconds", "system.process.started.time.seconds": 1743679, "system.process.threads": 1, "system.process.user": "www-data", "system.process.virtual.memory.bytes": 13189120}, {"status": "Up", "system.process": "nginx|nginx: worker process", "system.process.command": "nginx: worker process", "system.process.cpu.percent": 0, "system.process.handles": 31, "system.process.id": 1412601, "system.process.memory.used.bytes": 3723264, "system.process.memory.used.percent": 0, "system.process.name": "nginx", "system.process.started.time": " 20 days 4 hours 21 minutes 19 seconds", "system.process.started.time.seconds": 1743679, "system.process.threads": 1, "system.process.user": "www-data", "system.process.virtual.memory.bytes": 13189120}, {"status": "Up", "system.process": "nginx|nginx: worker process", "system.process.command": "nginx: worker process", "system.process.cpu.percent": 0, "system.process.handles": 31, "system.process.id": 1412604, "system.process.memory.used.bytes": 3461120, "system.process.memory.used.percent": 0, "system.process.name": "nginx", "system.process.started.time": " 20 days 4 hours 21 minutes 19 seconds", "system.process.started.time.seconds": 1743679, "system.process.threads": 1, "system.process.user": "www-data", "system.process.virtual.memory.bytes": 13189120}, {"status": "Up", "system.process": "nginx|nginx: worker process", "system.process.command": "nginx: worker process", "system.process.cpu.percent": 0, "system.process.handles": 31, "system.process.id": 1412606, "system.process.memory.used.bytes": 3723264, "system.process.memory.used.percent": 0, "system.process.name": "nginx", "system.process.started.time": " 20 days 4 hours 21 minutes 19 seconds", "system.process.started.time.seconds": 1743679, "system.process.threads": 1, "system.process.user": "www-data", "system.process.virtual.memory.bytes": 13189120}, {"status": "Up", "system.process": "standalo|/bin/sh /opt/wildfly/bin/standalone.sh -b=0.0.0.0", "system.process.command": "/bin/sh /opt/wildfly/bin/standalone.sh -b=0.0.0.0", "system.process.cpu.percent": 0, "system.process.handles": 9, "system.process.id": 1412828, "system.process.memory.used.bytes": 1835008, "system.process.memory.used.percent": 0, "system.process.name": "standalo", "system.process.started.time": " 20 days 4 hours 20 minutes 56 seconds", "system.process.started.time.seconds": 1743656, "system.process.threads": 1, "system.process.user": "j<PERSON>s", "system.process.virtual.memory.bytes": 2867200}, {"status": "Up", "system.process": "java|java -D[Standalone] -Xms64m -Xmx512m -XX:MetaspaceSize=96M -XX:MaxMetaspaceSize=256m -Djava.net.preferIPv4Stack=true -Djboss.modules.system.pkgs=org.jboss.byteman -Djava.awt.headless=true --add-exports=java.desktop/sun.awt=ALL-UNNAMED --add-exports=java.naming/com.sun.jndi.ldap=ALL-UNNAMED --add-exports=java.naming/com.sun.jndi.url.ldap=ALL-UNNAMED --add-exports=java.naming/com.sun.jndi.url.ldaps=ALL-UNNAMED --add-exports=jdk.naming.dns/com.sun.jndi.dns=ALL-UNNAMED --add-opens=java.base/java.lang=ALL-UNNAMED --add-opens=java.base/java.lang.invoke=ALL-UNNAMED --add-opens=java.base/java.lang.reflect=ALL-UNNAMED --add-opens=java.base/java.io=ALL-UNNAMED --add-opens=java.base/java.net=ALL-UNNAMED --add-opens=java.base/java.security=ALL-UNNAMED --add-opens=java.base/java.util=ALL-UNNAMED --add-opens=java.base/java.util.concurrent=ALL-UNNAMED --add-opens=java.management/javax.management=ALL-UNNAMED --add-opens=java.naming/javax.naming=ALL-UNNAMED -Djava.security.manager=allow -Dorg.jboss.boot.log.file=/opt/wildfly/standalone/log/server.log -Dlogging.configuration=file:/opt/wildfly/standalone/configuration/logging.properties -jar /opt/wildfly/jboss-modules.jar -mp /opt/wildfly/modules org.jboss.as.standalone -Djboss.home.dir=/opt/wildfly -Djboss.server.base.dir=/opt/wildfly/standalone -b=0.0.0.0", "system.process.command": "java -D[Standalone] -Xms64m -Xmx512m -XX:MetaspaceSize=96M -XX:MaxMetaspaceSize=256m -Djava.net.preferIPv4Stack=true -Djboss.modules.system.pkgs=org.jboss.byteman -Djava.awt.headless=true --add-exports=java.desktop/sun.awt=ALL-UNNAMED --add-exports=java.naming/com.sun.jndi.ldap=ALL-UNNAMED --add-exports=java.naming/com.sun.jndi.url.ldap=ALL-UNNAMED --add-exports=java.naming/com.sun.jndi.url.ldaps=ALL-UNNAMED --add-exports=jdk.naming.dns/com.sun.jndi.dns=ALL-UNNAMED --add-opens=java.base/java.lang=ALL-UNNAMED --add-opens=java.base/java.lang.invoke=ALL-UNNAMED --add-opens=java.base/java.lang.reflect=ALL-UNNAMED --add-opens=java.base/java.io=ALL-UNNAMED --add-opens=java.base/java.net=ALL-UNNAMED --add-opens=java.base/java.security=ALL-UNNAMED --add-opens=java.base/java.util=ALL-UNNAMED --add-opens=java.base/java.util.concurrent=ALL-UNNAMED --add-opens=java.management/javax.management=ALL-UNNAMED --add-opens=java.naming/javax.naming=ALL-UNNAMED -Djava.security.manager=allow -Dorg.jboss.boot.log.file=/opt/wildfly/standalone/log/server.log -Dlogging.configuration=file:/opt/wildfly/standalone/configuration/logging.properties -jar /opt/wildfly/jboss-modules.jar -mp /opt/wildfly/modules org.jboss.as.standalone -Djboss.home.dir=/opt/wildfly -Djboss.server.base.dir=/opt/wildfly/standalone -b=0.0.0.0", "system.process.cpu.percent": 0.0125, "system.process.handles": 569, "system.process.id": 1412943, "system.process.memory.used.bytes": 116396032, "system.process.memory.used.percent": 1.3, "system.process.name": "java", "system.process.started.time": " 20 days 4 hours 20 minutes 56 seconds", "system.process.started.time.seconds": 1743656, "system.process.threads": 73, "system.process.user": "j<PERSON>s", "system.process.virtual.memory.bytes": 1544192000}, {"status": "Up", "system.process": "postgres|postgres: 16/main: checkpointer", "system.process.command": "postgres: 16/main: checkpointer", "system.process.cpu.percent": 0, "system.process.handles": 55, "system.process.id": 1414371, "system.process.memory.used.bytes": 7938048, "system.process.memory.used.percent": 0, "system.process.name": "postgres", "system.process.started.time": " 20 days 4 hours 18 minutes 42 seconds", "system.process.started.time.seconds": 1743522, "system.process.threads": 1, "system.process.user": "postgres", "system.process.virtual.memory.bytes": 229085184}, {"status": "Up", "system.process": "postgres|postgres: 16/main: autovacuum launcher", "system.process.command": "postgres: 16/main: autovacuum launcher", "system.process.cpu.percent": 0, "system.process.handles": 56, "system.process.id": 1414375, "system.process.memory.used.bytes": 4136960, "system.process.memory.used.percent": 0, "system.process.name": "postgres", "system.process.started.time": " 20 days 4 hours 18 minutes 42 seconds", "system.process.started.time.seconds": 1743522, "system.process.threads": 1, "system.process.user": "postgres", "system.process.virtual.memory.bytes": 229826560}, {"status": "Up", "system.process": "nginx|nginx: worker process", "system.process.command": "nginx: worker process", "system.process.cpu.percent": 0, "system.process.handles": 31, "system.process.id": 1412605, "system.process.memory.used.bytes": 3461120, "system.process.memory.used.percent": 0, "system.process.name": "nginx", "system.process.started.time": " 20 days 4 hours 21 minutes 19 seconds", "system.process.started.time.seconds": 1743679, "system.process.threads": 1, "system.process.user": "www-data", "system.process.virtual.memory.bytes": 13189120}, {"status": "Up", "system.process": "postgres|/usr/lib/postgresql/16/bin/postgres -D /var/lib/postgresql/16/main -c config_file=/etc/postgresql/16/main/postgresql.conf", "system.process.command": "/usr/lib/postgresql/16/bin/postgres -D /var/lib/postgresql/16/main -c config_file=/etc/postgresql/16/main/postgresql.conf", "system.process.cpu.percent": 0, "system.process.handles": 58, "system.process.id": 1414370, "system.process.memory.used.bytes": 8781824, "system.process.memory.used.percent": 0.1, "system.process.name": "postgres", "system.process.started.time": " 20 days 4 hours 18 minutes 43 seconds", "system.process.started.time.seconds": 1743523, "system.process.threads": 1, "system.process.user": "postgres", "system.process.virtual.memory.bytes": 228179968}, {"status": "Up", "system.process": "postgres|postgres: 16/main: walwriter", "system.process.command": "postgres: 16/main: walwriter", "system.process.cpu.percent": 0, "system.process.handles": 55, "system.process.id": 1414374, "system.process.memory.used.bytes": 3481600, "system.process.memory.used.percent": 0, "system.process.name": "postgres", "system.process.started.time": " 20 days 4 hours 18 minutes 42 seconds", "system.process.started.time.seconds": 1743522, "system.process.threads": 1, "system.process.user": "postgres", "system.process.virtual.memory.bytes": 228315136}, {"status": "Up", "system.process": "nginx|nginx: worker process", "system.process.command": "nginx: worker process", "system.process.cpu.percent": 0, "system.process.handles": 31, "system.process.id": 1412602, "system.process.memory.used.bytes": 3461120, "system.process.memory.used.percent": 0, "system.process.name": "nginx", "system.process.started.time": " 20 days 4 hours 21 minutes 19 seconds", "system.process.started.time.seconds": 1743679, "system.process.threads": 1, "system.process.user": "www-data", "system.process.virtual.memory.bytes": 13189120}, {"status": "Up", "system.process": "postgres|postgres: 16/main: background writer", "system.process.command": "postgres: 16/main: background writer", "system.process.cpu.percent": 0, "system.process.handles": 54, "system.process.id": 1414372, "system.process.memory.used.bytes": 3612672, "system.process.memory.used.percent": 0, "system.process.name": "postgres", "system.process.started.time": " 20 days 4 hours 18 minutes 42 seconds", "system.process.started.time.seconds": 1743522, "system.process.threads": 1, "system.process.user": "postgres", "system.process.virtual.memory.bytes": 228315136}, {"status": "Up", "system.process": "postgres|postgres: 16/main: logical replication launcher", "system.process.command": "postgres: 16/main: logical replication launcher", "system.process.cpu.percent": 0, "system.process.handles": 56, "system.process.id": 1414376, "system.process.memory.used.bytes": 4268032, "system.process.memory.used.percent": 0, "system.process.name": "postgres", "system.process.started.time": " 20 days 4 hours 18 minutes 42 seconds", "system.process.started.time.seconds": 1743522, "system.process.threads": 1, "system.process.user": "postgres", "system.process.virtual.memory.bytes": 229806080}, {"status": "Up", "system.process": "nginx|nginx: master process /usr/sbin/nginx -g daemon on; master_process on;", "system.process.command": "nginx: master process /usr/sbin/nginx -g daemon on; master_process on;", "system.process.cpu.percent": 0, "system.process.handles": 34, "system.process.id": 1412598, "system.process.memory.used.bytes": 1757184, "system.process.memory.used.percent": 0, "system.process.name": "nginx", "system.process.started.time": " 20 days 4 hours 21 minutes 19 seconds", "system.process.started.time.seconds": 1743679, "system.process.threads": 1, "system.process.user": "root", "system.process.virtual.memory.bytes": 11423744}, {"status": "Up", "system.process": "nginx|nginx: worker process", "system.process.command": "nginx: worker process", "system.process.cpu.percent": 0, "system.process.handles": 31, "system.process.id": 1412600, "system.process.memory.used.bytes": 3461120, "system.process.memory.used.percent": 0, "system.process.name": "nginx", "system.process.started.time": " 20 days 4 hours 21 minutes 19 seconds", "system.process.started.time.seconds": 1743679, "system.process.threads": 1, "system.process.user": "www-data", "system.process.virtual.memory.bytes": 13189120}, {"status": "Up", "system.process": "nginx|nginx: worker process", "system.process.command": "nginx: worker process", "system.process.cpu.percent": 0, "system.process.handles": 31, "system.process.id": 1412603, "system.process.memory.used.bytes": 2936832, "system.process.memory.used.percent": 0, "system.process.name": "nginx", "system.process.started.time": " 20 days 4 hours 21 minutes 19 seconds", "system.process.started.time.seconds": 1743679, "system.process.threads": 1, "system.process.user": "www-data", "system.process.virtual.memory.bytes": 13189120}, {"status": "Down", "system.process": "postgres|postgres: 16/main: motadata motadata 127.0.0.1(51172) idle", "system.process.command": "postgres: 16/main: motadata motadata 127.0.0.1(51172) idle", "system.process.name": "postgres"}, {"status": "Down", "system.process": "postgres|postgres: 16/main: motadata motadata 127.0.0.1(51180) idle", "system.process.command": "postgres: 16/main: motadata motadata 127.0.0.1(51180) idle", "system.process.name": "postgres"}, {"status": "Down", "system.process": "postgres|postgres: 16/main: motadata motadata 127.0.0.1(51196) idle", "system.process.command": "postgres: 16/main: motadata motadata 127.0.0.1(51196) idle", "system.process.name": "postgres"}, {"status": "Down", "system.process": "postgres|postgres: 16/main: motadata motadata 127.0.0.1(51198) idle", "system.process.command": "postgres: 16/main: motadata motadata 127.0.0.1(51198) idle", "system.process.name": "postgres"}, {"status": "Down", "system.process": "postgres|postgres: 16/main: motadata motadata 127.0.0.1(51200) idle", "system.process.command": "postgres: 16/main: motadata motadata 127.0.0.1(51200) idle", "system.process.name": "postgres"}], "system.process.network.connection": [{"system.process": "postgres|/usr/lib/postgresql/16/bin/postgres -D /var/lib/postgresql/16/main -c config_file=/etc/postgresql/16/main/postgresql.conf", "system.process.destination.ip": "***********", "system.process.destination.port": "*", "system.process.source.ip": "***********", "system.process.source.port": "5432"}]}, {"correlation.metrics": ["system.process.network.connection"], "system.process": [{"status": "Up", "system.process": "nginx|nginx: worker process", "system.process.command": "nginx: worker process", "system.process.cpu.percent": 0, "system.process.handles": 31, "system.process.id": 1412599, "system.process.memory.used.bytes": 3461120, "system.process.memory.used.percent": 0, "system.process.name": "nginx", "system.process.started.time": " 20 days 4 hours 26 minutes 17 seconds", "system.process.started.time.seconds": 1743977, "system.process.threads": 1, "system.process.user": "www-data", "system.process.virtual.memory.bytes": 13189120}, {"status": "Up", "system.process": "nginx|nginx: worker process", "system.process.command": "nginx: worker process", "system.process.cpu.percent": 0, "system.process.handles": 31, "system.process.id": 1412600, "system.process.memory.used.bytes": 3461120, "system.process.memory.used.percent": 0, "system.process.name": "nginx", "system.process.started.time": " 20 days 4 hours 26 minutes 17 seconds", "system.process.started.time.seconds": 1743977, "system.process.threads": 1, "system.process.user": "www-data", "system.process.virtual.memory.bytes": 13189120}, {"status": "Up", "system.process": "nginx|nginx: worker process", "system.process.command": "nginx: worker process", "system.process.cpu.percent": 0, "system.process.handles": 31, "system.process.id": 1412603, "system.process.memory.used.bytes": 2936832, "system.process.memory.used.percent": 0, "system.process.name": "nginx", "system.process.started.time": " 20 days 4 hours 26 minutes 17 seconds", "system.process.started.time.seconds": 1743977, "system.process.threads": 1, "system.process.user": "www-data", "system.process.virtual.memory.bytes": 13189120}, {"status": "Up", "system.process": "postgres|postgres: 16/main: walwriter", "system.process.command": "postgres: 16/main: walwriter", "system.process.cpu.percent": 0, "system.process.handles": 55, "system.process.id": 1414374, "system.process.memory.used.bytes": 3481600, "system.process.memory.used.percent": 0, "system.process.name": "postgres", "system.process.started.time": " 20 days 4 hours 23 minutes 40 seconds", "system.process.started.time.seconds": 1743820, "system.process.threads": 1, "system.process.user": "postgres", "system.process.virtual.memory.bytes": 228315136}, {"status": "Up", "system.process": "nginx|nginx: master process /usr/sbin/nginx -g daemon on; master_process on;", "system.process.command": "nginx: master process /usr/sbin/nginx -g daemon on; master_process on;", "system.process.cpu.percent": 0, "system.process.handles": 34, "system.process.id": 1412598, "system.process.memory.used.bytes": 1757184, "system.process.memory.used.percent": 0, "system.process.name": "nginx", "system.process.started.time": " 20 days 4 hours 26 minutes 17 seconds", "system.process.started.time.seconds": 1743977, "system.process.threads": 1, "system.process.user": "root", "system.process.virtual.memory.bytes": 11423744}, {"status": "Up", "system.process": "nginx|nginx: worker process", "system.process.command": "nginx: worker process", "system.process.cpu.percent": 0, "system.process.handles": 31, "system.process.id": 1412604, "system.process.memory.used.bytes": 3461120, "system.process.memory.used.percent": 0, "system.process.name": "nginx", "system.process.started.time": " 20 days 4 hours 26 minutes 17 seconds", "system.process.started.time.seconds": 1743977, "system.process.threads": 1, "system.process.user": "www-data", "system.process.virtual.memory.bytes": 13189120}, {"status": "Up", "system.process": "postgres|postgres: 16/main: autovacuum launcher", "system.process.command": "postgres: 16/main: autovacuum launcher", "system.process.cpu.percent": 0, "system.process.handles": 56, "system.process.id": 1414375, "system.process.memory.used.bytes": 4136960, "system.process.memory.used.percent": 0, "system.process.name": "postgres", "system.process.started.time": " 20 days 4 hours 23 minutes 40 seconds", "system.process.started.time.seconds": 1743820, "system.process.threads": 1, "system.process.user": "postgres", "system.process.virtual.memory.bytes": 229826560}, {"status": "Up", "system.process": "postgres|postgres: 16/main: logical replication launcher", "system.process.command": "postgres: 16/main: logical replication launcher", "system.process.cpu.percent": 0, "system.process.handles": 56, "system.process.id": 1414376, "system.process.memory.used.bytes": 4268032, "system.process.memory.used.percent": 0, "system.process.name": "postgres", "system.process.started.time": " 20 days 4 hours 23 minutes 40 seconds", "system.process.started.time.seconds": 1743820, "system.process.threads": 1, "system.process.user": "postgres", "system.process.virtual.memory.bytes": 229806080}, {"status": "Up", "system.process": "postgres|postgres: 16/main: background writer", "system.process.command": "postgres: 16/main: background writer", "system.process.cpu.percent": 0, "system.process.handles": 54, "system.process.id": 1414372, "system.process.memory.used.bytes": 3612672, "system.process.memory.used.percent": 0, "system.process.name": "postgres", "system.process.started.time": " 20 days 4 hours 23 minutes 40 seconds", "system.process.started.time.seconds": 1743820, "system.process.threads": 1, "system.process.user": "postgres", "system.process.virtual.memory.bytes": 228315136}, {"status": "Up", "system.process": "nginx|nginx: worker process", "system.process.command": "nginx: worker process", "system.process.cpu.percent": 0, "system.process.handles": 31, "system.process.id": 1412601, "system.process.memory.used.bytes": 3723264, "system.process.memory.used.percent": 0, "system.process.name": "nginx", "system.process.started.time": " 20 days 4 hours 26 minutes 17 seconds", "system.process.started.time.seconds": 1743977, "system.process.threads": 1, "system.process.user": "www-data", "system.process.virtual.memory.bytes": 13189120}, {"status": "Up", "system.process": "standalo|/bin/sh /opt/wildfly/bin/standalone.sh -b=0.0.0.0", "system.process.command": "/bin/sh /opt/wildfly/bin/standalone.sh -b=0.0.0.0", "system.process.cpu.percent": 0, "system.process.handles": 9, "system.process.id": 1412828, "system.process.memory.used.bytes": 1835008, "system.process.memory.used.percent": 0, "system.process.name": "standalo", "system.process.started.time": " 20 days 4 hours 25 minutes 54 seconds", "system.process.started.time.seconds": 1743954, "system.process.threads": 1, "system.process.user": "j<PERSON>s", "system.process.virtual.memory.bytes": 2867200}, {"status": "Up", "system.process": "java|java -D[Standalone] -Xms64m -Xmx512m -XX:MetaspaceSize=96M -XX:MaxMetaspaceSize=256m -Djava.net.preferIPv4Stack=true -Djboss.modules.system.pkgs=org.jboss.byteman -Djava.awt.headless=true --add-exports=java.desktop/sun.awt=ALL-UNNAMED --add-exports=java.naming/com.sun.jndi.ldap=ALL-UNNAMED --add-exports=java.naming/com.sun.jndi.url.ldap=ALL-UNNAMED --add-exports=java.naming/com.sun.jndi.url.ldaps=ALL-UNNAMED --add-exports=jdk.naming.dns/com.sun.jndi.dns=ALL-UNNAMED --add-opens=java.base/java.lang=ALL-UNNAMED --add-opens=java.base/java.lang.invoke=ALL-UNNAMED --add-opens=java.base/java.lang.reflect=ALL-UNNAMED --add-opens=java.base/java.io=ALL-UNNAMED --add-opens=java.base/java.net=ALL-UNNAMED --add-opens=java.base/java.security=ALL-UNNAMED --add-opens=java.base/java.util=ALL-UNNAMED --add-opens=java.base/java.util.concurrent=ALL-UNNAMED --add-opens=java.management/javax.management=ALL-UNNAMED --add-opens=java.naming/javax.naming=ALL-UNNAMED -Djava.security.manager=allow -Dorg.jboss.boot.log.file=/opt/wildfly/standalone/log/server.log -Dlogging.configuration=file:/opt/wildfly/standalone/configuration/logging.properties -jar /opt/wildfly/jboss-modules.jar -mp /opt/wildfly/modules org.jboss.as.standalone -Djboss.home.dir=/opt/wildfly -Djboss.server.base.dir=/opt/wildfly/standalone -b=0.0.0.0", "system.process.command": "java -D[Standalone] -Xms64m -Xmx512m -XX:MetaspaceSize=96M -XX:MaxMetaspaceSize=256m -Djava.net.preferIPv4Stack=true -Djboss.modules.system.pkgs=org.jboss.byteman -Djava.awt.headless=true --add-exports=java.desktop/sun.awt=ALL-UNNAMED --add-exports=java.naming/com.sun.jndi.ldap=ALL-UNNAMED --add-exports=java.naming/com.sun.jndi.url.ldap=ALL-UNNAMED --add-exports=java.naming/com.sun.jndi.url.ldaps=ALL-UNNAMED --add-exports=jdk.naming.dns/com.sun.jndi.dns=ALL-UNNAMED --add-opens=java.base/java.lang=ALL-UNNAMED --add-opens=java.base/java.lang.invoke=ALL-UNNAMED --add-opens=java.base/java.lang.reflect=ALL-UNNAMED --add-opens=java.base/java.io=ALL-UNNAMED --add-opens=java.base/java.net=ALL-UNNAMED --add-opens=java.base/java.security=ALL-UNNAMED --add-opens=java.base/java.util=ALL-UNNAMED --add-opens=java.base/java.util.concurrent=ALL-UNNAMED --add-opens=java.management/javax.management=ALL-UNNAMED --add-opens=java.naming/javax.naming=ALL-UNNAMED -Djava.security.manager=allow -Dorg.jboss.boot.log.file=/opt/wildfly/standalone/log/server.log -Dlogging.configuration=file:/opt/wildfly/standalone/configuration/logging.properties -jar /opt/wildfly/jboss-modules.jar -mp /opt/wildfly/modules org.jboss.as.standalone -Djboss.home.dir=/opt/wildfly -Djboss.server.base.dir=/opt/wildfly/standalone -b=0.0.0.0", "system.process.cpu.percent": 0.0125, "system.process.handles": 569, "system.process.id": 1412943, "system.process.memory.used.bytes": 114429952, "system.process.memory.used.percent": 1.3, "system.process.name": "java", "system.process.started.time": " 20 days 4 hours 25 minutes 53 seconds", "system.process.started.time.seconds": 1743953, "system.process.threads": 73, "system.process.user": "j<PERSON>s", "system.process.virtual.memory.bytes": 1544192000}, {"status": "Up", "system.process": "postgres|/usr/lib/postgresql/16/bin/postgres -D /var/lib/postgresql/16/main -c config_file=/etc/postgresql/16/main/postgresql.conf", "system.process.command": "/usr/lib/postgresql/16/bin/postgres -D /var/lib/postgresql/16/main -c config_file=/etc/postgresql/16/main/postgresql.conf", "system.process.cpu.percent": 0, "system.process.handles": 58, "system.process.id": 1414370, "system.process.memory.used.bytes": 8781824, "system.process.memory.used.percent": 0.1, "system.process.name": "postgres", "system.process.started.time": " 20 days 4 hours 23 minutes 41 seconds", "system.process.started.time.seconds": 1743821, "system.process.threads": 1, "system.process.user": "postgres", "system.process.virtual.memory.bytes": 228179968}, {"status": "Up", "system.process": "postgres|postgres: 16/main: checkpointer", "system.process.command": "postgres: 16/main: checkpointer", "system.process.cpu.percent": 0, "system.process.handles": 55, "system.process.id": 1414371, "system.process.memory.used.bytes": 8331264, "system.process.memory.used.percent": 0.1, "system.process.name": "postgres", "system.process.started.time": " 20 days 4 hours 23 minutes 40 seconds", "system.process.started.time.seconds": 1743820, "system.process.threads": 1, "system.process.user": "postgres", "system.process.virtual.memory.bytes": 229085184}, {"status": "Up", "system.process": "nginx|nginx: worker process", "system.process.command": "nginx: worker process", "system.process.cpu.percent": 0, "system.process.handles": 31, "system.process.id": 1412602, "system.process.memory.used.bytes": 3461120, "system.process.memory.used.percent": 0, "system.process.name": "nginx", "system.process.started.time": " 20 days 4 hours 26 minutes 17 seconds", "system.process.started.time.seconds": 1743977, "system.process.threads": 1, "system.process.user": "www-data", "system.process.virtual.memory.bytes": 13189120}, {"status": "Up", "system.process": "nginx|nginx: worker process", "system.process.command": "nginx: worker process", "system.process.cpu.percent": 0, "system.process.handles": 31, "system.process.id": 1412605, "system.process.memory.used.bytes": 3461120, "system.process.memory.used.percent": 0, "system.process.name": "nginx", "system.process.started.time": " 20 days 4 hours 26 minutes 17 seconds", "system.process.started.time.seconds": 1743977, "system.process.threads": 1, "system.process.user": "www-data", "system.process.virtual.memory.bytes": 13189120}, {"status": "Up", "system.process": "nginx|nginx: worker process", "system.process.command": "nginx: worker process", "system.process.cpu.percent": 0, "system.process.handles": 31, "system.process.id": 1412606, "system.process.memory.used.bytes": 3723264, "system.process.memory.used.percent": 0, "system.process.name": "nginx", "system.process.started.time": " 20 days 4 hours 26 minutes 17 seconds", "system.process.started.time.seconds": 1743977, "system.process.threads": 1, "system.process.user": "www-data", "system.process.virtual.memory.bytes": 13189120}, {"status": "Down", "system.process": "postgres|postgres: 16/main: motadata motadata 127.0.0.1(51172) idle", "system.process.command": "postgres: 16/main: motadata motadata 127.0.0.1(51172) idle", "system.process.name": "postgres"}, {"status": "Down", "system.process": "postgres|postgres: 16/main: motadata motadata 127.0.0.1(51180) idle", "system.process.command": "postgres: 16/main: motadata motadata 127.0.0.1(51180) idle", "system.process.name": "postgres"}, {"status": "Down", "system.process": "postgres|postgres: 16/main: motadata motadata 127.0.0.1(51196) idle", "system.process.command": "postgres: 16/main: motadata motadata 127.0.0.1(51196) idle", "system.process.name": "postgres"}, {"status": "Down", "system.process": "postgres|postgres: 16/main: motadata motadata 127.0.0.1(51198) idle", "system.process.command": "postgres: 16/main: motadata motadata 127.0.0.1(51198) idle", "system.process.name": "postgres"}, {"status": "Down", "system.process": "postgres|postgres: 16/main: motadata motadata 127.0.0.1(51200) idle", "system.process.command": "postgres: 16/main: motadata motadata 127.0.0.1(51200) idle", "system.process.name": "postgres"}], "system.process.network.connection": [{"system.process": "postgres|/usr/lib/postgresql/16/bin/postgres -D /var/lib/postgresql/16/main -c config_file=/etc/postgresql/16/main/postgresql.conf", "system.process.destination.ip": "***********", "system.process.destination.port": "*", "system.process.source.ip": "***********", "system.process.source.port": "5432"}]}, {"correlation.metrics": ["system.process.network.connection"], "system.process": [{"status": "Up", "system.process": "nginx|nginx: worker process", "system.process.command": "nginx: worker process", "system.process.cpu.percent": 0, "system.process.handles": 31, "system.process.id": 1412600, "system.process.memory.used.bytes": 3461120, "system.process.memory.used.percent": 0, "system.process.name": "nginx", "system.process.started.time": " 20 days 4 hours 31 minutes 20 seconds", "system.process.started.time.seconds": 1744280, "system.process.threads": 1, "system.process.user": "www-data", "system.process.virtual.memory.bytes": 13189120}, {"status": "Up", "system.process": "nginx|nginx: worker process", "system.process.command": "nginx: worker process", "system.process.cpu.percent": 0, "system.process.handles": 31, "system.process.id": 1412603, "system.process.memory.used.bytes": 2936832, "system.process.memory.used.percent": 0, "system.process.name": "nginx", "system.process.started.time": " 20 days 4 hours 31 minutes 20 seconds", "system.process.started.time.seconds": 1744280, "system.process.threads": 1, "system.process.user": "www-data", "system.process.virtual.memory.bytes": 13189120}, {"status": "Up", "system.process": "nginx|nginx: worker process", "system.process.command": "nginx: worker process", "system.process.cpu.percent": 0, "system.process.handles": 31, "system.process.id": 1412606, "system.process.memory.used.bytes": 3723264, "system.process.memory.used.percent": 0, "system.process.name": "nginx", "system.process.started.time": " 20 days 4 hours 31 minutes 20 seconds", "system.process.started.time.seconds": 1744280, "system.process.threads": 1, "system.process.user": "www-data", "system.process.virtual.memory.bytes": 13189120}, {"status": "Up", "system.process": "java|java -D[Standalone] -Xms64m -Xmx512m -XX:MetaspaceSize=96M -XX:MaxMetaspaceSize=256m -Djava.net.preferIPv4Stack=true -Djboss.modules.system.pkgs=org.jboss.byteman -Djava.awt.headless=true --add-exports=java.desktop/sun.awt=ALL-UNNAMED --add-exports=java.naming/com.sun.jndi.ldap=ALL-UNNAMED --add-exports=java.naming/com.sun.jndi.url.ldap=ALL-UNNAMED --add-exports=java.naming/com.sun.jndi.url.ldaps=ALL-UNNAMED --add-exports=jdk.naming.dns/com.sun.jndi.dns=ALL-UNNAMED --add-opens=java.base/java.lang=ALL-UNNAMED --add-opens=java.base/java.lang.invoke=ALL-UNNAMED --add-opens=java.base/java.lang.reflect=ALL-UNNAMED --add-opens=java.base/java.io=ALL-UNNAMED --add-opens=java.base/java.net=ALL-UNNAMED --add-opens=java.base/java.security=ALL-UNNAMED --add-opens=java.base/java.util=ALL-UNNAMED --add-opens=java.base/java.util.concurrent=ALL-UNNAMED --add-opens=java.management/javax.management=ALL-UNNAMED --add-opens=java.naming/javax.naming=ALL-UNNAMED -Djava.security.manager=allow -Dorg.jboss.boot.log.file=/opt/wildfly/standalone/log/server.log -Dlogging.configuration=file:/opt/wildfly/standalone/configuration/logging.properties -jar /opt/wildfly/jboss-modules.jar -mp /opt/wildfly/modules org.jboss.as.standalone -Djboss.home.dir=/opt/wildfly -Djboss.server.base.dir=/opt/wildfly/standalone -b=0.0.0.0", "system.process.command": "java -D[Standalone] -Xms64m -Xmx512m -XX:MetaspaceSize=96M -XX:MaxMetaspaceSize=256m -Djava.net.preferIPv4Stack=true -Djboss.modules.system.pkgs=org.jboss.byteman -Djava.awt.headless=true --add-exports=java.desktop/sun.awt=ALL-UNNAMED --add-exports=java.naming/com.sun.jndi.ldap=ALL-UNNAMED --add-exports=java.naming/com.sun.jndi.url.ldap=ALL-UNNAMED --add-exports=java.naming/com.sun.jndi.url.ldaps=ALL-UNNAMED --add-exports=jdk.naming.dns/com.sun.jndi.dns=ALL-UNNAMED --add-opens=java.base/java.lang=ALL-UNNAMED --add-opens=java.base/java.lang.invoke=ALL-UNNAMED --add-opens=java.base/java.lang.reflect=ALL-UNNAMED --add-opens=java.base/java.io=ALL-UNNAMED --add-opens=java.base/java.net=ALL-UNNAMED --add-opens=java.base/java.security=ALL-UNNAMED --add-opens=java.base/java.util=ALL-UNNAMED --add-opens=java.base/java.util.concurrent=ALL-UNNAMED --add-opens=java.management/javax.management=ALL-UNNAMED --add-opens=java.naming/javax.naming=ALL-UNNAMED -Djava.security.manager=allow -Dorg.jboss.boot.log.file=/opt/wildfly/standalone/log/server.log -Dlogging.configuration=file:/opt/wildfly/standalone/configuration/logging.properties -jar /opt/wildfly/jboss-modules.jar -mp /opt/wildfly/modules org.jboss.as.standalone -Djboss.home.dir=/opt/wildfly -Djboss.server.base.dir=/opt/wildfly/standalone -b=0.0.0.0", "system.process.cpu.percent": 0.0125, "system.process.handles": 569, "system.process.id": 1412943, "system.process.memory.used.bytes": 73273344, "system.process.memory.used.percent": 0.8, "system.process.name": "java", "system.process.started.time": " 20 days 4 hours 30 minutes 57 seconds", "system.process.started.time.seconds": 1744257, "system.process.threads": 73, "system.process.user": "j<PERSON>s", "system.process.virtual.memory.bytes": 1544192000}, {"status": "Up", "system.process": "nginx|nginx: worker process", "system.process.command": "nginx: worker process", "system.process.cpu.percent": 0, "system.process.handles": 31, "system.process.id": 1412599, "system.process.memory.used.bytes": 3461120, "system.process.memory.used.percent": 0, "system.process.name": "nginx", "system.process.started.time": " 20 days 4 hours 31 minutes 20 seconds", "system.process.started.time.seconds": 1744280, "system.process.threads": 1, "system.process.user": "www-data", "system.process.virtual.memory.bytes": 13189120}, {"status": "Up", "system.process": "nginx|nginx: worker process", "system.process.command": "nginx: worker process", "system.process.cpu.percent": 0, "system.process.handles": 31, "system.process.id": 1412602, "system.process.memory.used.bytes": 3461120, "system.process.memory.used.percent": 0, "system.process.name": "nginx", "system.process.started.time": " 20 days 4 hours 31 minutes 20 seconds", "system.process.started.time.seconds": 1744280, "system.process.threads": 1, "system.process.user": "www-data", "system.process.virtual.memory.bytes": 13189120}, {"status": "Up", "system.process": "postgres|postgres: 16/main: background writer", "system.process.command": "postgres: 16/main: background writer", "system.process.cpu.percent": 0, "system.process.handles": 54, "system.process.id": 1414372, "system.process.memory.used.bytes": 3612672, "system.process.memory.used.percent": 0, "system.process.name": "postgres", "system.process.started.time": " 20 days 4 hours 28 minutes 43 seconds", "system.process.started.time.seconds": 1744123, "system.process.threads": 1, "system.process.user": "postgres", "system.process.virtual.memory.bytes": 228315136}, {"status": "Up", "system.process": "postgres|postgres: 16/main: walwriter", "system.process.command": "postgres: 16/main: walwriter", "system.process.cpu.percent": 0, "system.process.handles": 55, "system.process.id": 1414374, "system.process.memory.used.bytes": 3481600, "system.process.memory.used.percent": 0, "system.process.name": "postgres", "system.process.started.time": " 20 days 4 hours 28 minutes 43 seconds", "system.process.started.time.seconds": 1744123, "system.process.threads": 1, "system.process.user": "postgres", "system.process.virtual.memory.bytes": 228315136}, {"status": "Up", "system.process": "nginx|nginx: master process /usr/sbin/nginx -g daemon on; master_process on;", "system.process.command": "nginx: master process /usr/sbin/nginx -g daemon on; master_process on;", "system.process.cpu.percent": 0, "system.process.handles": 34, "system.process.id": 1412598, "system.process.memory.used.bytes": 1757184, "system.process.memory.used.percent": 0, "system.process.name": "nginx", "system.process.started.time": " 20 days 4 hours 31 minutes 20 seconds", "system.process.started.time.seconds": 1744280, "system.process.threads": 1, "system.process.user": "root", "system.process.virtual.memory.bytes": 11423744}, {"status": "Up", "system.process": "nginx|nginx: worker process", "system.process.command": "nginx: worker process", "system.process.cpu.percent": 0, "system.process.handles": 31, "system.process.id": 1412601, "system.process.memory.used.bytes": 3723264, "system.process.memory.used.percent": 0, "system.process.name": "nginx", "system.process.started.time": " 20 days 4 hours 31 minutes 20 seconds", "system.process.started.time.seconds": 1744280, "system.process.threads": 1, "system.process.user": "www-data", "system.process.virtual.memory.bytes": 13189120}, {"status": "Up", "system.process": "nginx|nginx: worker process", "system.process.command": "nginx: worker process", "system.process.cpu.percent": 0, "system.process.handles": 31, "system.process.id": 1412604, "system.process.memory.used.bytes": 3461120, "system.process.memory.used.percent": 0, "system.process.name": "nginx", "system.process.started.time": " 20 days 4 hours 31 minutes 20 seconds", "system.process.started.time.seconds": 1744280, "system.process.threads": 1, "system.process.user": "www-data", "system.process.virtual.memory.bytes": 13189120}, {"status": "Up", "system.process": "postgres|/usr/lib/postgresql/16/bin/postgres -D /var/lib/postgresql/16/main -c config_file=/etc/postgresql/16/main/postgresql.conf", "system.process.command": "/usr/lib/postgresql/16/bin/postgres -D /var/lib/postgresql/16/main -c config_file=/etc/postgresql/16/main/postgresql.conf", "system.process.cpu.percent": 0, "system.process.handles": 58, "system.process.id": 1414370, "system.process.memory.used.bytes": 8781824, "system.process.memory.used.percent": 0.1, "system.process.name": "postgres", "system.process.started.time": " 20 days 4 hours 28 minutes 44 seconds", "system.process.started.time.seconds": 1744124, "system.process.threads": 1, "system.process.user": "postgres", "system.process.virtual.memory.bytes": 228179968}, {"status": "Up", "system.process": "postgres|postgres: 16/main: autovacuum launcher", "system.process.command": "postgres: 16/main: autovacuum launcher", "system.process.cpu.percent": 0, "system.process.handles": 56, "system.process.id": 1414375, "system.process.memory.used.bytes": 4136960, "system.process.memory.used.percent": 0, "system.process.name": "postgres", "system.process.started.time": " 20 days 4 hours 28 minutes 43 seconds", "system.process.started.time.seconds": 1744123, "system.process.threads": 1, "system.process.user": "postgres", "system.process.virtual.memory.bytes": 229826560}, {"status": "Up", "system.process": "nginx|nginx: worker process", "system.process.command": "nginx: worker process", "system.process.cpu.percent": 0, "system.process.handles": 31, "system.process.id": 1412605, "system.process.memory.used.bytes": 3461120, "system.process.memory.used.percent": 0, "system.process.name": "nginx", "system.process.started.time": " 20 days 4 hours 31 minutes 20 seconds", "system.process.started.time.seconds": 1744280, "system.process.threads": 1, "system.process.user": "www-data", "system.process.virtual.memory.bytes": 13189120}, {"status": "Up", "system.process": "standalo|/bin/sh /opt/wildfly/bin/standalone.sh -b=0.0.0.0", "system.process.command": "/bin/sh /opt/wildfly/bin/standalone.sh -b=0.0.0.0", "system.process.cpu.percent": 0, "system.process.handles": 9, "system.process.id": 1412828, "system.process.memory.used.bytes": 1835008, "system.process.memory.used.percent": 0, "system.process.name": "standalo", "system.process.started.time": " 20 days 4 hours 30 minutes 57 seconds", "system.process.started.time.seconds": 1744257, "system.process.threads": 1, "system.process.user": "j<PERSON>s", "system.process.virtual.memory.bytes": 2867200}, {"status": "Up", "system.process": "postgres|postgres: 16/main: checkpointer", "system.process.command": "postgres: 16/main: checkpointer", "system.process.cpu.percent": 0, "system.process.handles": 55, "system.process.id": 1414371, "system.process.memory.used.bytes": 8200192, "system.process.memory.used.percent": 0, "system.process.name": "postgres", "system.process.started.time": " 20 days 4 hours 28 minutes 43 seconds", "system.process.started.time.seconds": 1744123, "system.process.threads": 1, "system.process.user": "postgres", "system.process.virtual.memory.bytes": 229085184}, {"status": "Up", "system.process": "postgres|postgres: 16/main: logical replication launcher", "system.process.command": "postgres: 16/main: logical replication launcher", "system.process.cpu.percent": 0, "system.process.handles": 56, "system.process.id": 1414376, "system.process.memory.used.bytes": 4268032, "system.process.memory.used.percent": 0, "system.process.name": "postgres", "system.process.started.time": " 20 days 4 hours 28 minutes 43 seconds", "system.process.started.time.seconds": 1744123, "system.process.threads": 1, "system.process.user": "postgres", "system.process.virtual.memory.bytes": 229806080}, {"status": "Down", "system.process": "postgres|postgres: 16/main: motadata motadata 127.0.0.1(51172) idle", "system.process.command": "postgres: 16/main: motadata motadata 127.0.0.1(51172) idle", "system.process.name": "postgres"}, {"status": "Down", "system.process": "postgres|postgres: 16/main: motadata motadata 127.0.0.1(51180) idle", "system.process.command": "postgres: 16/main: motadata motadata 127.0.0.1(51180) idle", "system.process.name": "postgres"}, {"status": "Down", "system.process": "postgres|postgres: 16/main: motadata motadata 127.0.0.1(51196) idle", "system.process.command": "postgres: 16/main: motadata motadata 127.0.0.1(51196) idle", "system.process.name": "postgres"}, {"status": "Down", "system.process": "postgres|postgres: 16/main: motadata motadata 127.0.0.1(51198) idle", "system.process.command": "postgres: 16/main: motadata motadata 127.0.0.1(51198) idle", "system.process.name": "postgres"}, {"status": "Down", "system.process": "postgres|postgres: 16/main: motadata motadata 127.0.0.1(51200) idle", "system.process.command": "postgres: 16/main: motadata motadata 127.0.0.1(51200) idle", "system.process.name": "postgres"}], "system.process.network.connection": [{"system.process": "postgres|/usr/lib/postgresql/16/bin/postgres -D /var/lib/postgresql/16/main -c config_file=/etc/postgresql/16/main/postgresql.conf", "system.process.destination.ip": "***********", "system.process.destination.port": "*", "system.process.source.ip": "***********", "system.process.source.port": "5432"}]}, {"correlation.metrics": ["system.process.network.connection"], "system.process": [{"status": "Up", "system.process": "nginx|nginx: worker process", "system.process.command": "nginx: worker process", "system.process.cpu.percent": 0, "system.process.handles": 31, "system.process.id": 1412602, "system.process.memory.used.bytes": 3461120, "system.process.memory.used.percent": 0, "system.process.name": "nginx", "system.process.started.time": " 20 days 4 hours 41 minutes 18 seconds", "system.process.started.time.seconds": 1744878, "system.process.threads": 1, "system.process.user": "www-data", "system.process.virtual.memory.bytes": 13189120}, {"status": "Up", "system.process": "standalo|/bin/sh /opt/wildfly/bin/standalone.sh -b=0.0.0.0", "system.process.command": "/bin/sh /opt/wildfly/bin/standalone.sh -b=0.0.0.0", "system.process.cpu.percent": 0, "system.process.handles": 9, "system.process.id": 1412828, "system.process.memory.used.bytes": 1835008, "system.process.memory.used.percent": 0, "system.process.name": "standalo", "system.process.started.time": " 20 days 4 hours 40 minutes 56 seconds", "system.process.started.time.seconds": 1744856, "system.process.threads": 1, "system.process.user": "j<PERSON>s", "system.process.virtual.memory.bytes": 2867200}, {"status": "Up", "system.process": "postgres|postgres: 16/main: background writer", "system.process.command": "postgres: 16/main: background writer", "system.process.cpu.percent": 0, "system.process.handles": 54, "system.process.id": 1414372, "system.process.memory.used.bytes": 3612672, "system.process.memory.used.percent": 0, "system.process.name": "postgres", "system.process.started.time": " 20 days 4 hours 38 minutes 42 seconds", "system.process.started.time.seconds": 1744722, "system.process.threads": 1, "system.process.user": "postgres", "system.process.virtual.memory.bytes": 228315136}, {"status": "Up", "system.process": "postgres|postgres: 16/main: logical replication launcher", "system.process.command": "postgres: 16/main: logical replication launcher", "system.process.cpu.percent": 0, "system.process.handles": 56, "system.process.id": 1414376, "system.process.memory.used.bytes": 4268032, "system.process.memory.used.percent": 0, "system.process.name": "postgres", "system.process.started.time": " 20 days 4 hours 38 minutes 42 seconds", "system.process.started.time.seconds": 1744722, "system.process.threads": 1, "system.process.user": "postgres", "system.process.virtual.memory.bytes": 229806080}, {"status": "Up", "system.process": "nginx|nginx: worker process", "system.process.command": "nginx: worker process", "system.process.cpu.percent": 0, "system.process.handles": 31, "system.process.id": 1412603, "system.process.memory.used.bytes": 2936832, "system.process.memory.used.percent": 0, "system.process.name": "nginx", "system.process.started.time": " 20 days 4 hours 41 minutes 18 seconds", "system.process.started.time.seconds": 1744878, "system.process.threads": 1, "system.process.user": "www-data", "system.process.virtual.memory.bytes": 13189120}, {"status": "Up", "system.process": "nginx|nginx: worker process", "system.process.command": "nginx: worker process", "system.process.cpu.percent": 0, "system.process.handles": 31, "system.process.id": 1412605, "system.process.memory.used.bytes": 3461120, "system.process.memory.used.percent": 0, "system.process.name": "nginx", "system.process.started.time": " 20 days 4 hours 41 minutes 18 seconds", "system.process.started.time.seconds": 1744878, "system.process.threads": 1, "system.process.user": "www-data", "system.process.virtual.memory.bytes": 13189120}, {"status": "Up", "system.process": "nginx|nginx: worker process", "system.process.command": "nginx: worker process", "system.process.cpu.percent": 0, "system.process.handles": 31, "system.process.id": 1412606, "system.process.memory.used.bytes": 3723264, "system.process.memory.used.percent": 0, "system.process.name": "nginx", "system.process.started.time": " 20 days 4 hours 41 minutes 18 seconds", "system.process.started.time.seconds": 1744878, "system.process.threads": 1, "system.process.user": "www-data", "system.process.virtual.memory.bytes": 13189120}, {"status": "Up", "system.process": "java|java -D[Standalone] -Xms64m -Xmx512m -XX:MetaspaceSize=96M -XX:MaxMetaspaceSize=256m -Djava.net.preferIPv4Stack=true -Djboss.modules.system.pkgs=org.jboss.byteman -Djava.awt.headless=true --add-exports=java.desktop/sun.awt=ALL-UNNAMED --add-exports=java.naming/com.sun.jndi.ldap=ALL-UNNAMED --add-exports=java.naming/com.sun.jndi.url.ldap=ALL-UNNAMED --add-exports=java.naming/com.sun.jndi.url.ldaps=ALL-UNNAMED --add-exports=jdk.naming.dns/com.sun.jndi.dns=ALL-UNNAMED --add-opens=java.base/java.lang=ALL-UNNAMED --add-opens=java.base/java.lang.invoke=ALL-UNNAMED --add-opens=java.base/java.lang.reflect=ALL-UNNAMED --add-opens=java.base/java.io=ALL-UNNAMED --add-opens=java.base/java.net=ALL-UNNAMED --add-opens=java.base/java.security=ALL-UNNAMED --add-opens=java.base/java.util=ALL-UNNAMED --add-opens=java.base/java.util.concurrent=ALL-UNNAMED --add-opens=java.management/javax.management=ALL-UNNAMED --add-opens=java.naming/javax.naming=ALL-UNNAMED -Djava.security.manager=allow -Dorg.jboss.boot.log.file=/opt/wildfly/standalone/log/server.log -Dlogging.configuration=file:/opt/wildfly/standalone/configuration/logging.properties -jar /opt/wildfly/jboss-modules.jar -mp /opt/wildfly/modules org.jboss.as.standalone -Djboss.home.dir=/opt/wildfly -Djboss.server.base.dir=/opt/wildfly/standalone -b=0.0.0.0", "system.process.command": "java -D[Standalone] -Xms64m -Xmx512m -XX:MetaspaceSize=96M -XX:MaxMetaspaceSize=256m -Djava.net.preferIPv4Stack=true -Djboss.modules.system.pkgs=org.jboss.byteman -Djava.awt.headless=true --add-exports=java.desktop/sun.awt=ALL-UNNAMED --add-exports=java.naming/com.sun.jndi.ldap=ALL-UNNAMED --add-exports=java.naming/com.sun.jndi.url.ldap=ALL-UNNAMED --add-exports=java.naming/com.sun.jndi.url.ldaps=ALL-UNNAMED --add-exports=jdk.naming.dns/com.sun.jndi.dns=ALL-UNNAMED --add-opens=java.base/java.lang=ALL-UNNAMED --add-opens=java.base/java.lang.invoke=ALL-UNNAMED --add-opens=java.base/java.lang.reflect=ALL-UNNAMED --add-opens=java.base/java.io=ALL-UNNAMED --add-opens=java.base/java.net=ALL-UNNAMED --add-opens=java.base/java.security=ALL-UNNAMED --add-opens=java.base/java.util=ALL-UNNAMED --add-opens=java.base/java.util.concurrent=ALL-UNNAMED --add-opens=java.management/javax.management=ALL-UNNAMED --add-opens=java.naming/javax.naming=ALL-UNNAMED -Djava.security.manager=allow -Dorg.jboss.boot.log.file=/opt/wildfly/standalone/log/server.log -Dlogging.configuration=file:/opt/wildfly/standalone/configuration/logging.properties -jar /opt/wildfly/jboss-modules.jar -mp /opt/wildfly/modules org.jboss.as.standalone -Djboss.home.dir=/opt/wildfly -Djboss.server.base.dir=/opt/wildfly/standalone -b=0.0.0.0", "system.process.cpu.percent": 0.0125, "system.process.handles": 569, "system.process.id": 1412943, "system.process.memory.used.bytes": 69079040, "system.process.memory.used.percent": 0.8, "system.process.name": "java", "system.process.started.time": " 20 days 4 hours 40 minutes 55 seconds", "system.process.started.time.seconds": 1744855, "system.process.threads": 73, "system.process.user": "j<PERSON>s", "system.process.virtual.memory.bytes": 1544192000}, {"status": "Up", "system.process": "nginx|nginx: worker process", "system.process.command": "nginx: worker process", "system.process.cpu.percent": 0, "system.process.handles": 31, "system.process.id": 1412601, "system.process.memory.used.bytes": 3723264, "system.process.memory.used.percent": 0, "system.process.name": "nginx", "system.process.started.time": " 20 days 4 hours 41 minutes 18 seconds", "system.process.started.time.seconds": 1744878, "system.process.threads": 1, "system.process.user": "www-data", "system.process.virtual.memory.bytes": 13189120}, {"status": "Up", "system.process": "postgres|postgres: 16/main: checkpointer", "system.process.command": "postgres: 16/main: checkpointer", "system.process.cpu.percent": 0, "system.process.handles": 55, "system.process.id": 1414371, "system.process.memory.used.bytes": 8200192, "system.process.memory.used.percent": 0, "system.process.name": "postgres", "system.process.started.time": " 20 days 4 hours 38 minutes 42 seconds", "system.process.started.time.seconds": 1744722, "system.process.threads": 1, "system.process.user": "postgres", "system.process.virtual.memory.bytes": 229085184}, {"status": "Up", "system.process": "postgres|postgres: 16/main: walwriter", "system.process.command": "postgres: 16/main: walwriter", "system.process.cpu.percent": 0, "system.process.handles": 55, "system.process.id": 1414374, "system.process.memory.used.bytes": 3481600, "system.process.memory.used.percent": 0, "system.process.name": "postgres", "system.process.started.time": " 20 days 4 hours 38 minutes 42 seconds", "system.process.started.time.seconds": 1744722, "system.process.threads": 1, "system.process.user": "postgres", "system.process.virtual.memory.bytes": 228315136}, {"status": "Up", "system.process": "postgres|/usr/lib/postgresql/16/bin/postgres -D /var/lib/postgresql/16/main -c config_file=/etc/postgresql/16/main/postgresql.conf", "system.process.command": "/usr/lib/postgresql/16/bin/postgres -D /var/lib/postgresql/16/main -c config_file=/etc/postgresql/16/main/postgresql.conf", "system.process.cpu.percent": 0, "system.process.handles": 58, "system.process.id": 1414370, "system.process.memory.used.bytes": 8781824, "system.process.memory.used.percent": 0.1, "system.process.name": "postgres", "system.process.started.time": " 20 days 4 hours 38 minutes 42 seconds", "system.process.started.time.seconds": 1744722, "system.process.threads": 1, "system.process.user": "postgres", "system.process.virtual.memory.bytes": 228179968}, {"status": "Up", "system.process": "nginx|nginx: master process /usr/sbin/nginx -g daemon on; master_process on;", "system.process.command": "nginx: master process /usr/sbin/nginx -g daemon on; master_process on;", "system.process.cpu.percent": 0, "system.process.handles": 34, "system.process.id": 1412598, "system.process.memory.used.bytes": 1757184, "system.process.memory.used.percent": 0, "system.process.name": "nginx", "system.process.started.time": " 20 days 4 hours 41 minutes 18 seconds", "system.process.started.time.seconds": 1744878, "system.process.threads": 1, "system.process.user": "root", "system.process.virtual.memory.bytes": 11423744}, {"status": "Up", "system.process": "nginx|nginx: worker process", "system.process.command": "nginx: worker process", "system.process.cpu.percent": 0, "system.process.handles": 31, "system.process.id": 1412599, "system.process.memory.used.bytes": 3461120, "system.process.memory.used.percent": 0, "system.process.name": "nginx", "system.process.started.time": " 20 days 4 hours 41 minutes 18 seconds", "system.process.started.time.seconds": 1744878, "system.process.threads": 1, "system.process.user": "www-data", "system.process.virtual.memory.bytes": 13189120}, {"status": "Up", "system.process": "nginx|nginx: worker process", "system.process.command": "nginx: worker process", "system.process.cpu.percent": 0, "system.process.handles": 31, "system.process.id": 1412600, "system.process.memory.used.bytes": 3461120, "system.process.memory.used.percent": 0, "system.process.name": "nginx", "system.process.started.time": " 20 days 4 hours 41 minutes 18 seconds", "system.process.started.time.seconds": 1744878, "system.process.threads": 1, "system.process.user": "www-data", "system.process.virtual.memory.bytes": 13189120}, {"status": "Up", "system.process": "nginx|nginx: worker process", "system.process.command": "nginx: worker process", "system.process.cpu.percent": 0, "system.process.handles": 31, "system.process.id": 1412604, "system.process.memory.used.bytes": 3461120, "system.process.memory.used.percent": 0, "system.process.name": "nginx", "system.process.started.time": " 20 days 4 hours 41 minutes 18 seconds", "system.process.started.time.seconds": 1744878, "system.process.threads": 1, "system.process.user": "www-data", "system.process.virtual.memory.bytes": 13189120}, {"status": "Up", "system.process": "postgres|postgres: 16/main: autovacuum launcher", "system.process.command": "postgres: 16/main: autovacuum launcher", "system.process.cpu.percent": 0, "system.process.handles": 56, "system.process.id": 1414375, "system.process.memory.used.bytes": 4136960, "system.process.memory.used.percent": 0, "system.process.name": "postgres", "system.process.started.time": " 20 days 4 hours 38 minutes 42 seconds", "system.process.started.time.seconds": 1744722, "system.process.threads": 1, "system.process.user": "postgres", "system.process.virtual.memory.bytes": 229826560}, {"status": "Down", "system.process": "postgres|postgres: 16/main: motadata motadata 127.0.0.1(51172) idle", "system.process.command": "postgres: 16/main: motadata motadata 127.0.0.1(51172) idle", "system.process.name": "postgres"}, {"status": "Down", "system.process": "postgres|postgres: 16/main: motadata motadata 127.0.0.1(51180) idle", "system.process.command": "postgres: 16/main: motadata motadata 127.0.0.1(51180) idle", "system.process.name": "postgres"}, {"status": "Down", "system.process": "postgres|postgres: 16/main: motadata motadata 127.0.0.1(51196) idle", "system.process.command": "postgres: 16/main: motadata motadata 127.0.0.1(51196) idle", "system.process.name": "postgres"}, {"status": "Down", "system.process": "postgres|postgres: 16/main: motadata motadata 127.0.0.1(51198) idle", "system.process.command": "postgres: 16/main: motadata motadata 127.0.0.1(51198) idle", "system.process.name": "postgres"}, {"status": "Down", "system.process": "postgres|postgres: 16/main: motadata motadata 127.0.0.1(51200) idle", "system.process.command": "postgres: 16/main: motadata motadata 127.0.0.1(51200) idle", "system.process.name": "postgres"}], "system.process.network.connection": [{"system.process": "postgres|/usr/lib/postgresql/16/bin/postgres -D /var/lib/postgresql/16/main -c config_file=/etc/postgresql/16/main/postgresql.conf", "system.process.destination.ip": "***********", "system.process.destination.port": "*", "system.process.source.ip": "***********", "system.process.source.port": "5432"}]}], "rediscovery": null}}